<?php
session_start();
require_once 'config.php';

echo "<h2>Quick Login and Event Test</h2>";

// Auto-login as admin for testing
if (!isset($_SESSION['admin_id'])) {
    echo "<p>Logging in as admin...</p>";
    
    try {
        $stmt = $pdo->prepare("SELECT id, username, password FROM admins WHERE username = ?");
        $stmt->execute(['admin']);
        $admin = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($admin && password_verify('admin123', $admin['password'])) {
            $_SESSION['admin_id'] = $admin['id'];
            $_SESSION['admin_username'] = $admin['username'];
            echo "<p>✅ Logged in successfully as admin (ID: {$admin['id']})</p>";
        } else {
            echo "<p>❌ Login failed</p>";
            exit;
        }
    } catch (Exception $e) {
        echo "<p>❌ Login error: " . $e->getMessage() . "</p>";
        exit;
    }
} else {
    echo "<p>✅ Already logged in as admin (ID: {$_SESSION['admin_id']})</p>";
}

// Now test event creation
echo "<h3>Testing Event Creation</h3>";

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<h4>POST Data:</h4>";
    echo "<pre>" . print_r($_POST, true) . "</pre>";
    
    try {
        $pdo->beginTransaction();
        
        // Test the exact same query as create_event_with_materials.php
        $stmt = $pdo->prepare("
            INSERT INTO events (title, description, event_date, location,
                              max_attendees, category_id, created_by, is_active)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $data = [
            $_POST['title'],
            !empty($_POST['description']) ? $_POST['description'] : null,
            $_POST['start_datetime'],
            !empty($_POST['location']) ? $_POST['location'] : null,
            !empty($_POST['capacity']) ? (int)$_POST['capacity'] : null,
            !empty($_POST['category_id']) ? (int)$_POST['category_id'] : null,
            $_SESSION['admin_id'],
            ($_POST['status'] === 'published') ? 1 : 0
        ];
        
        echo "<h4>Data being inserted:</h4>";
        echo "<pre>";
        foreach (['title', 'description', 'event_date', 'location', 'max_attendees', 'category_id', 'created_by', 'is_active'] as $i => $field) {
            echo "$field: " . var_export($data[$i], true) . "\n";
        }
        echo "</pre>";
        
        $result = $stmt->execute($data);
        
        if ($result) {
            $event_id = $pdo->lastInsertId();
            echo "<p>✅ SUCCESS! Event created with ID: $event_id</p>";
            
            // Clean up
            $pdo->prepare("DELETE FROM events WHERE id = ?")->execute([$event_id]);
            echo "<p>✅ Test event cleaned up</p>";
            
            $pdo->commit();
        } else {
            echo "<p>❌ Query failed</p>";
            echo "<p>Error: " . print_r($stmt->errorInfo(), true) . "</p>";
            $pdo->rollback();
        }
        
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollback();
        }
        echo "<p>❌ Exception: " . $e->getMessage() . "</p>";
        echo "<p>File: " . $e->getFile() . " Line: " . $e->getLine() . "</p>";
    }
} else {
    ?>
    <form method="POST">
        <h4>Create Test Event</h4>
        <table>
            <tr><td>Title:</td><td><input type="text" name="title" value="Quick Test Event" required></td></tr>
            <tr><td>Description:</td><td><textarea name="description">Test description</textarea></td></tr>
            <tr><td>Start DateTime:</td><td><input type="datetime-local" name="start_datetime" value="2025-07-20T10:00" required></td></tr>
            <tr><td>End DateTime:</td><td><input type="datetime-local" name="end_datetime" value="2025-07-20T12:00" required></td></tr>
            <tr><td>Location:</td><td><input type="text" name="location" value="Test Location"></td></tr>
            <tr><td>Capacity:</td><td><input type="number" name="capacity" value="50"></td></tr>
            <tr><td>Category ID:</td><td><input type="number" name="category_id" value=""></td></tr>
            <tr><td>Status:</td><td>
                <select name="status">
                    <option value="published">Published</option>
                    <option value="draft">Draft</option>
                </select>
            </td></tr>
        </table>
        <p><button type="submit">Create Test Event</button></p>
    </form>
    
    <h3>Also Test the Original AJAX Endpoint</h3>
    <button onclick="testAjaxEndpoint()">Test AJAX Event Creation</button>
    <div id="ajax-result"></div>
    
    <script>
    function testAjaxEndpoint() {
        const formData = new FormData();
        formData.append('title', 'AJAX Test Event');
        formData.append('description', 'AJAX test description');
        formData.append('start_datetime', '2025-07-20T10:00');
        formData.append('end_datetime', '2025-07-20T12:00');
        formData.append('location', 'AJAX Test Location');
        formData.append('capacity', '50');
        formData.append('status', 'published');
        
        fetch('admin/create_event_with_materials.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.text())
        .then(data => {
            document.getElementById('ajax-result').innerHTML = '<h4>AJAX Response:</h4><pre>' + data + '</pre>';
        })
        .catch(error => {
            document.getElementById('ajax-result').innerHTML = '<h4>AJAX Error:</h4><pre>' + error + '</pre>';
        });
    }
    </script>
    <?php
}
?>
