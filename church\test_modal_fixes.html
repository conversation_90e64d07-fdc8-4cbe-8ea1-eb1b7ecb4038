<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Event Modal Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>Event Modal Test</h1>
        <p>This page tests the event modal fixes for:</p>
        <ul>
            <li>✅ Header banner display (fixed)</li>
            <li>🔧 Document display (testing)</li>
            <li>🔧 Requirements/Notes display (testing)</li>
        </ul>

        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Event 16: Evening Service</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>Has:</strong> Promotional materials + Requirements</p>
                        <p><strong>Expected:</strong> Should show header banner, promotional materials, and requirements section</p>
                        <button class="btn btn-primary" onclick="testEvent(16)">Test Event 16</button>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Event 4: Summer Weekend</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>Has:</strong> Documents + Requirements</p>
                        <p><strong>Expected:</strong> Should show documents section and requirements section</p>
                        <button class="btn btn-primary" onclick="testEvent(4)">Test Event 4</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-4">
            <h3>Test Results:</h3>
            <div id="testResults" class="alert alert-info">
                Click a test button above to see results...
            </div>
        </div>
    </div>

    <!-- Modal for displaying event details -->
    <div class="modal fade" id="eventModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="eventModalTitle">Event Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="eventModalBody">
                    Loading...
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function testEvent(eventId) {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = `<div class="spinner-border" role="status"><span class="visually-hidden">Loading...</span></div> Testing Event ${eventId}...`;
            
            // Fetch event details
            fetch(`user/get_event_details.php?id=${eventId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Display in modal
                        document.getElementById('eventModalTitle').textContent = data.event.title;
                        document.getElementById('eventModalBody').innerHTML = data.html;
                        
                        // Show modal
                        const modal = new bootstrap.Modal(document.getElementById('eventModal'));
                        modal.show();
                        
                        // Update test results
                        let results = `<h4>✅ Event ${eventId}: ${data.event.title}</h4>`;
                        results += `<ul>`;
                        results += `<li><strong>Header Banner:</strong> ${data.html.includes('img') && data.html.includes('alt=') ? '✅ Found' : '❌ Not found'}</li>`;
                        results += `<li><strong>Requirements:</strong> ${data.html.includes('Requirements & Notes') ? '✅ Found' : '❌ Not found'}</li>`;
                        results += `<li><strong>Documents:</strong> ${data.html.includes('Documents & Resources') ? '✅ Found' : '❌ Not found'}</li>`;
                        results += `<li><strong>Promotional Materials:</strong> ${data.html.includes('Promotional Materials') ? '✅ Found' : '❌ Not found'}</li>`;
                        results += `</ul>`;
                        
                        resultsDiv.innerHTML = results;
                        resultsDiv.className = 'alert alert-success';
                    } else {
                        resultsDiv.innerHTML = `<h4>❌ Error testing Event ${eventId}</h4><p>${data.message}</p>`;
                        resultsDiv.className = 'alert alert-danger';
                    }
                })
                .catch(error => {
                    resultsDiv.innerHTML = `<h4>❌ Network Error</h4><p>${error.message}</p>`;
                    resultsDiv.className = 'alert alert-danger';
                });
        }
    </script>
</body>
</html>
