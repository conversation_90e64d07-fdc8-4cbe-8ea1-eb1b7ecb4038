<?php
session_start();
require_once 'config.php';

// Auto-login as admin
if (!isset($_SESSION['admin_id'])) {
    $stmt = $pdo->prepare("SELECT id, username, password FROM admins WHERE username = ?");
    $stmt->execute(['admin']);
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($admin && password_verify('admin123', $admin['password'])) {
        $_SESSION['admin_id'] = $admin['id'];
        $_SESSION['admin_username'] = $admin['username'];
    }
}

echo "<h2>Admin File Upload Test</h2>";
echo "<p>✅ Logged in as admin (ID: {$_SESSION['admin_id']})</p>";

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['test_file'])) {
    echo "<h3>Testing File Upload with Admin Interface Simulation</h3>";
    
    // Create test form data exactly like the admin interface
    $postData = [
        'title' => 'Admin Test Event',
        'description' => 'Testing admin file upload',
        'start_datetime' => '2025-07-20T10:00',
        'end_datetime' => '2025-07-20T12:00',
        'location' => 'Admin Test Location',
        'capacity' => '50',
        'status' => 'published',
        'has_promotional_files' => '1'
    ];
    
    // Prepare file for upload
    $uploadedFile = $_FILES['test_file'];
    
    // Create a temporary file to simulate the admin interface upload
    $tempFile = tempnam(sys_get_temp_dir(), 'admin_test_');
    move_uploaded_file($uploadedFile['tmp_name'], $tempFile);
    
    // Simulate the exact FormData structure from admin interface
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost/campaign/church/admin/create_event_with_materials.php');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    
    // Set session cookie
    $session_name = session_name();
    $session_id = session_id();
    curl_setopt($ch, CURLOPT_COOKIE, "$session_name=$session_id");
    
    // Prepare multipart form data exactly like JavaScript FormData
    $postFields = $postData;
    $postFields['promotional_files[]'] = new CURLFile($tempFile, $uploadedFile['type'], $uploadedFile['name']);
    
    curl_setopt($ch, CURLOPT_POSTFIELDS, $postFields);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    // Clean up temp file
    unlink($tempFile);
    
    echo "<p><strong>HTTP Response Code:</strong> $http_code</p>";
    if ($error) {
        echo "<p><strong>cURL Error:</strong> $error</p>";
    }
    echo "<p><strong>Response:</strong></p>";
    echo "<pre>" . htmlspecialchars($response) . "</pre>";
    
    // Parse JSON response
    $result = json_decode($response, true);
    if ($result) {
        if ($result['success']) {
            echo "<p>✅ <strong>SUCCESS!</strong> Event created with file upload through admin interface simulation</p>";
            if (!empty($result['uploaded_files'])) {
                echo "<p>✅ Files uploaded: " . count($result['uploaded_files']) . "</p>";
                foreach ($result['uploaded_files'] as $file) {
                    echo "<p>  - " . $file['name'] . " (" . $file['category'] . ")</p>";
                }
            }
            if (!empty($result['upload_errors'])) {
                echo "<p>⚠️ Upload errors:</p>";
                foreach ($result['upload_errors'] as $error) {
                    echo "<p>  - $error</p>";
                }
            }
        } else {
            echo "<p>❌ <strong>FAILED:</strong> " . $result['message'] . "</p>";
        }
    } else {
        echo "<p>❌ Invalid JSON response</p>";
    }
    
} else {
    ?>
    <h3>Test Admin Interface File Upload</h3>
    <p>This simulates exactly how the admin interface uploads files with events.</p>
    
    <form method="POST" enctype="multipart/form-data">
        <p>Select a file to upload with the event:</p>
        <p><input type="file" name="test_file" accept="image/*,.pdf" required></p>
        <p><button type="submit">Test Admin File Upload</button></p>
    </form>
    
    <h3>Debug Information</h3>
    <p><strong>Current Session:</strong></p>
    <ul>
        <li>Admin ID: <?php echo $_SESSION['admin_id']; ?></li>
        <li>Session ID: <?php echo session_id(); ?></li>
        <li>Session Name: <?php echo session_name(); ?></li>
    </ul>
    
    <p><strong>PHP Upload Settings:</strong></p>
    <ul>
        <li>upload_max_filesize: <?php echo ini_get('upload_max_filesize'); ?></li>
        <li>post_max_size: <?php echo ini_get('post_max_size'); ?></li>
        <li>max_file_uploads: <?php echo ini_get('max_file_uploads'); ?></li>
    </ul>
    <?php
}
?>
