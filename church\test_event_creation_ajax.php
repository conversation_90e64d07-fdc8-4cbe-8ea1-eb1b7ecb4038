<!DOCTYPE html>
<html>
<head>
    <title>Test Event Creation AJAX</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
</head>
<body>
    <h2>Test Event Creation AJAX</h2>
    
    <div id="loginSection">
        <h3>Step 1: Login</h3>
        <form id="loginForm">
            <input type="text" id="username" placeholder="Username" value="admin"><br><br>
            <input type="password" id="password" placeholder="Password" value="admin123"><br><br>
            <button type="submit">Login</button>
        </form>
        <div id="loginResult"></div>
    </div>
    
    <div id="eventSection" style="display:none;">
        <h3>Step 2: Create Event</h3>
        <form id="eventForm">
            <input type="text" id="title" placeholder="Event Title" value="Test Event AJAX"><br><br>
            <textarea id="description" placeholder="Description">Test Description</textarea><br><br>
            <input type="datetime-local" id="start_datetime" value="2025-07-20T10:00"><br><br>
            <input type="datetime-local" id="end_datetime" value="2025-07-20T12:00"><br><br>
            <input type="text" id="location" placeholder="Location" value="Test Location"><br><br>
            <input type="number" id="capacity" placeholder="Capacity" value="50"><br><br>
            <select id="status">
                <option value="published">Published</option>
                <option value="draft">Draft</option>
            </select><br><br>
            <button type="submit">Create Event</button>
        </form>
        <div id="eventResult"></div>
    </div>
    
    <div id="debugInfo">
        <h3>Debug Information</h3>
        <div id="debug"></div>
    </div>

    <script>
    // Step 1: Login
    $('#loginForm').on('submit', function(e) {
        e.preventDefault();
        
        $.ajax({
            url: 'admin/login.php',
            method: 'POST',
            data: {
                username: $('#username').val(),
                password: $('#password').val()
            },
            success: function(response) {
                $('#loginResult').html('<div style="color: green;">Login successful! Response: ' + response.substring(0, 200) + '...</div>');
                $('#eventSection').show();
                $('#debug').append('<p>Login Response Length: ' + response.length + '</p>');
            },
            error: function(xhr, status, error) {
                $('#loginResult').html('<div style="color: red;">Login failed: ' + error + '</div>');
                $('#debug').append('<p>Login Error: ' + xhr.responseText + '</p>');
            }
        });
    });
    
    // Step 2: Create Event
    $('#eventForm').on('submit', function(e) {
        e.preventDefault();
        
        var formData = new FormData();
        formData.append('title', $('#title').val());
        formData.append('description', $('#description').val());
        formData.append('start_datetime', $('#start_datetime').val());
        formData.append('end_datetime', $('#end_datetime').val());
        formData.append('location', $('#location').val());
        formData.append('capacity', $('#capacity').val());
        formData.append('status', $('#status').val());
        
        // Test both endpoints
        testEventCreation('admin/create_event_with_materials.php', formData);
    });
    
    function testEventCreation(endpoint, formData) {
        $('#eventResult').html('<div>Testing endpoint: ' + endpoint + '</div>');
        
        $.ajax({
            url: endpoint,
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                $('#eventResult').append('<div style="color: green;">Success! Response: <pre>' + JSON.stringify(response, null, 2) + '</pre></div>');
            },
            error: function(xhr, status, error) {
                $('#eventResult').append('<div style="color: red;">Error: ' + error + '<br>Response: ' + xhr.responseText + '</div>');
                $('#debug').append('<p>Event Creation Error (' + endpoint + '): ' + xhr.responseText + '</p>');
            }
        });
    }
    
    // Test session status
    $.ajax({
        url: 'admin/check_session.php',
        method: 'GET',
        success: function(response) {
            $('#debug').append('<p>Session Status: ' + response + '</p>');
        },
        error: function() {
            $('#debug').append('<p>Could not check session status</p>');
        }
    });
    </script>
</body>
</html>
