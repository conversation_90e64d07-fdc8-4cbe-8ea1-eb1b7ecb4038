<?php
/**
 * Comprehensive fix for all birthday templates
 */

require_once 'config.php';

echo "<h2>Comprehensive Birthday Template Fix</h2>\n";

try {
    // Get all templates that might be birthday related
    $stmt = $pdo->prepare("
        SELECT id, template_name, content, is_birthday_template
        FROM email_templates 
        WHERE is_birthday_template = 1 
           OR template_name LIKE '%Birthday%' 
           OR template_name LIKE '%Notification%'
        ORDER BY template_name
    ");
    $stmt->execute();
    $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($templates)) {
        echo "<p>❌ No birthday templates found!</p>\n";
        exit;
    }
    
    echo "<p>Found " . count($templates) . " template(s) to check.</p>\n";
    
    $fixedCount = 0;
    
    foreach ($templates as $template) {
        echo "<div style='border: 2px solid #007bff; margin: 20px 0; padding: 15px;'>\n";
        echo "<h3>Template: " . htmlspecialchars($template['template_name']) . " (ID: {$template['id']})</h3>\n";
        echo "<p><strong>Is Birthday Template:</strong> " . ($template['is_birthday_template'] ? 'Yes' : 'No') . "</p>\n";
        
        $originalContent = $template['content'];
        $fixedContent = $originalContent;
        
        // Multiple comprehensive fixes
        $hasChanges = false;
        
        // Fix 1: Replace any hardcoded localhost image URLs
        $pattern1 = '/http:\/\/localhost\/[^"\']*uploads\/[^"\']*\.(png|jpg|jpeg|gif)/i';
        if (preg_match($pattern1, $fixedContent)) {
            $fixedContent = preg_replace($pattern1, '{birthday_member_image}', $fixedContent);
            $hasChanges = true;
            echo "<p>✅ Fixed hardcoded localhost URLs</p>\n";
        }
        
        // Fix 2: Replace any relative upload paths
        $pattern2 = '/uploads\/[a-zA-Z0-9]+\.(png|jpg|jpeg|gif)/i';
        if (preg_match($pattern2, $fixedContent)) {
            $fixedContent = preg_replace($pattern2, '{birthday_member_image}', $fixedContent);
            $hasChanges = true;
            echo "<p>✅ Fixed relative upload paths</p>\n";
        }
        
        // Fix 3: Replace img tags with hardcoded src
        $pattern3 = '/<img([^>]+)src=["\'][^"\']*(?:localhost|uploads)[^"\']*["\']([^>]*)>/i';
        if (preg_match($pattern3, $fixedContent)) {
            $fixedContent = preg_replace($pattern3, '<img$1src="{birthday_member_image}"$2>', $fixedContent);
            $hasChanges = true;
            echo "<p>✅ Fixed img tags with hardcoded src</p>\n";
        }
        
        // Fix 4: Replace any remaining hardcoded image references
        $pattern4 = '/["\'][^"\']*uploads\/[^"\']*\.(png|jpg|jpeg|gif)["\']?/i';
        if (preg_match($pattern4, $fixedContent)) {
            $fixedContent = preg_replace($pattern4, '"{birthday_member_image}"', $fixedContent);
            $hasChanges = true;
            echo "<p>✅ Fixed remaining hardcoded image references</p>\n";
        }
        
        // Fix 5: Ensure proper HTML structure for images
        if (strpos($fixedContent, '{birthday_member_image}') !== false) {
            // Make sure the image placeholder is properly formatted in img tags
            $fixedContent = preg_replace(
                '/(<img[^>]+src=["\']){birthday_member_image}(["\'][^>]*>)/i',
                '$1{birthday_member_image}$2',
                $fixedContent
            );
        }
        
        // Fix 6: Ensure template is marked as birthday template
        $isBirthdayTemplate = $template['is_birthday_template'];
        if (!$isBirthdayTemplate && 
            (strpos(strtolower($template['template_name']), 'birthday') !== false ||
             strpos(strtolower($template['template_name']), 'notification') !== false)) {
            $isBirthdayTemplate = 1;
            echo "<p>✅ Marking as birthday template</p>\n";
        }
        
        if ($hasChanges || $isBirthdayTemplate != $template['is_birthday_template']) {
            if ($hasChanges) {
                echo "<h4>Fixed Content:</h4>\n";
                echo "<pre style='background-color: #e6ffe6; padding: 10px; border: 1px solid #99ff99; max-height: 200px; overflow-y: auto;'>\n";
                echo htmlspecialchars($fixedContent);
                echo "</pre>\n";
            }
            
            // Update the database
            $updateStmt = $pdo->prepare("UPDATE email_templates SET content = ?, is_birthday_template = ? WHERE id = ?");
            $result = $updateStmt->execute([$fixedContent, $isBirthdayTemplate, $template['id']]);
            
            if ($result) {
                echo "<p style='color: green;'>✅ <strong>Template updated successfully in database!</strong></p>\n";
                $fixedCount++;
            } else {
                echo "<p style='color: red;'>❌ <strong>Failed to update template in database!</strong></p>\n";
            }
        } else {
            echo "<p style='color: green;'>✅ <strong>Template is already correct!</strong></p>\n";
        }
        
        echo "</div>\n";
    }
    
    // Clear any potential cache
    if (function_exists('opcache_reset')) {
        opcache_reset();
        echo "<p>✅ Cleared OPcache</p>\n";
    }
    
    // Summary
    echo "<div style='background-color: #d4edda; color: #155724; padding: 15px; border: 1px solid #c3e6cb; border-radius: 4px; margin: 20px 0;'>\n";
    echo "<h3>🎉 Fix Complete!</h3>\n";
    echo "<ul>\n";
    echo "<li><strong>Templates Processed:</strong> " . count($templates) . "</li>\n";
    echo "<li><strong>Templates Fixed:</strong> $fixedCount</li>\n";
    echo "<li><strong>Templates Already OK:</strong> " . (count($templates) - $fixedCount) . "</li>\n";
    echo "</ul>\n";
    echo "</div>\n";
    
    echo "<div style='background-color: #cce5ff; padding: 15px; border: 1px solid #99ccff; border-radius: 4px; margin: 20px 0;'>\n";
    echo "<h3>🔄 Next Steps</h3>\n";
    echo "<ol>\n";
    echo "<li><strong>Clear browser cache</strong> (Ctrl+Shift+R)</li>\n";
    echo "<li><strong>Go back to the admin birthday notification page</strong></li>\n";
    echo "<li><strong>Refresh the page</strong> (F5 or Ctrl+R)</li>\n";
    echo "<li><strong>Click on Ndivhuwo Machiba's birthday notification</strong></li>\n";
    echo "<li><strong>Select the template from the dropdown</strong></li>\n";
    echo "<li><strong>Check the preview</strong> - it should now show the actual member image</li>\n";
    echo "</ol>\n";
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<p>❌ <strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>\n";
}
?>
