<?php
/**
 * Check the actual content of birthday templates in the database
 */

require_once 'config.php';

echo "<h2>Current Template Content in Database</h2>\n";

try {
    // Get all birthday templates
    $stmt = $pdo->prepare("
        SELECT id, template_name, content, subject
        FROM email_templates 
        WHERE is_birthday_template = 1
        ORDER BY template_name
    ");
    $stmt->execute();
    $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($templates)) {
        echo "<p>❌ No birthday templates found!</p>\n";
        exit;
    }
    
    foreach ($templates as $template) {
        echo "<div style='border: 2px solid #007bff; margin: 20px 0; padding: 15px;'>\n";
        echo "<h3>Template: " . htmlspecialchars($template['template_name']) . " (ID: {$template['id']})</h3>\n";
        echo "<h4>Subject:</h4>\n";
        echo "<pre style='background-color: #f8f9fa; padding: 10px; border: 1px solid #dee2e6;'>\n";
        echo htmlspecialchars($template['subject']);
        echo "</pre>\n";
        
        echo "<h4>Content:</h4>\n";
        echo "<pre style='background-color: #f8f9fa; padding: 10px; border: 1px solid #dee2e6; max-height: 400px; overflow-y: auto;'>\n";
        echo htmlspecialchars($template['content']);
        echo "</pre>\n";
        
        // Check for hardcoded URLs
        if (strpos($template['content'], 'localhost') !== false || 
            strpos($template['content'], 'uploads/') !== false) {
            echo "<p style='color: red;'>❌ <strong>STILL CONTAINS HARDCODED URLS!</strong></p>\n";
        } else {
            echo "<p style='color: green;'>✅ <strong>No hardcoded URLs found</strong></p>\n";
        }
        
        // Check for placeholders
        if (strpos($template['content'], '{birthday_member_image}') !== false) {
            echo "<p style='color: green;'>✅ <strong>Contains {birthday_member_image} placeholder</strong></p>\n";
        } else {
            echo "<p style='color: orange;'>⚠️ <strong>No {birthday_member_image} placeholder found</strong></p>\n";
        }
        
        echo "</div>\n";
    }
    
} catch (Exception $e) {
    echo "<p>❌ <strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
}
?>
