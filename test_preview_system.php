<?php
/**
 * Test the preview system directly
 */

require_once 'config.php';

echo "<h2>Test Preview System</h2>\n";

// Simulate the preview system logic
$template_id = 46;

try {
    // Get template
    $stmt = $pdo->prepare("SELECT * FROM email_templates WHERE id = ?");
    $stmt->execute([$template_id]);
    $template = $stmt->fetch();
    
    if (!$template) {
        echo "<p>❌ Template not found!</p>\n";
        exit;
    }
    
    echo "<div style='border: 2px solid #007bff; margin: 20px 0; padding: 15px;'>\n";
    echo "<h3>Template: " . htmlspecialchars($template['template_name']) . " (ID: {$template['id']})</h3>\n";
    echo "<p><strong>Is Birthday Template:</strong> " . ($template['is_birthday_template'] ? 'Yes' : 'No') . "</p>\n";
    
    // Test the condition logic
    $isNotificationTemplate = strpos(strtolower($template['template_name']), 'notification') !== false;
    $isBirthdayTemplate = isset($template['is_birthday_template']) && $template['is_birthday_template'];
    $hasBirthdayInName = strpos(strtolower($template['template_name']), 'birthday') !== false;
    
    echo "<p><strong>Contains 'notification':</strong> " . ($isNotificationTemplate ? 'Yes' : 'No') . "</p>\n";
    echo "<p><strong>Is birthday template flag:</strong> " . ($isBirthdayTemplate ? 'Yes' : 'No') . "</p>\n";
    echo "<p><strong>Contains 'birthday':</strong> " . ($hasBirthdayInName ? 'Yes' : 'No') . "</p>\n";
    
    $shouldApplyBirthdayLogic = $isNotificationTemplate || $isBirthdayTemplate || $hasBirthdayInName;
    echo "<p><strong>Should apply birthday logic:</strong> " . ($shouldApplyBirthdayLogic ? 'Yes' : 'No') . "</p>\n";
    
    if ($shouldApplyBirthdayLogic) {
        echo "<p style='color: green;'>✅ Birthday member image replacement will be applied</p>\n";
        
        // Get a test member (Ndivhuwo)
        $stmt = $pdo->prepare("SELECT * FROM members WHERE full_name LIKE '%Ndivhuwo%' LIMIT 1");
        $stmt->execute();
        $testMember = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($testMember) {
            echo "<h4>Test Member: " . htmlspecialchars($testMember['full_name']) . "</h4>\n";
            echo "<p><strong>Image Path:</strong> " . htmlspecialchars($testMember['image_path'] ?? 'No image') . "</p>\n";
            
            if (!empty($testMember['image_path'])) {
                $birthdayMemberImagePath = '../' . ltrim($testMember['image_path'], '/');
                echo "<p><strong>Preview Image Path:</strong> " . htmlspecialchars($birthdayMemberImagePath) . "</p>\n";
                
                // Test if the file exists
                $fullPath = __DIR__ . '/' . $birthdayMemberImagePath;
                if (file_exists($fullPath)) {
                    echo "<p style='color: green;'>✅ Image file exists</p>\n";
                } else {
                    echo "<p style='color: red;'>❌ Image file NOT found at: " . htmlspecialchars($fullPath) . "</p>\n";
                    
                    // Try alternative paths
                    $altPath1 = __DIR__ . '/uploads/' . basename($testMember['image_path']);
                    $altPath2 = __DIR__ . '/' . $testMember['image_path'];
                    
                    if (file_exists($altPath1)) {
                        echo "<p style='color: orange;'>⚠️ Found at alternative path 1: " . htmlspecialchars($altPath1) . "</p>\n";
                    } elseif (file_exists($altPath2)) {
                        echo "<p style='color: orange;'>⚠️ Found at alternative path 2: " . htmlspecialchars($altPath2) . "</p>\n";
                    }
                }
                
                // Test the replacement
                $testContent = $template['content'];
                $testContent = str_replace('{birthday_member_image}', $birthdayMemberImagePath, $testContent);
                
                echo "<h4>Content After Replacement:</h4>\n";
                echo "<pre style='background-color: #f8f9fa; padding: 10px; border: 1px solid #dee2e6; max-height: 300px; overflow-y: auto;'>\n";
                echo htmlspecialchars($testContent);
                echo "</pre>\n";
                
                // Check if replacement worked
                if (strpos($testContent, $birthdayMemberImagePath) !== false) {
                    echo "<p style='color: green;'>✅ Replacement successful - image path found in content</p>\n";
                } else {
                    echo "<p style='color: red;'>❌ Replacement failed - image path not found in content</p>\n";
                }
                
                if (strpos($testContent, '{birthday_member_image}') !== false) {
                    echo "<p style='color: red;'>❌ Placeholder still exists in content - replacement incomplete</p>\n";
                } else {
                    echo "<p style='color: green;'>✅ All placeholders replaced</p>\n";
                }
            }
        }
    } else {
        echo "<p style='color: red;'>❌ Birthday member image replacement will NOT be applied</p>\n";
    }
    
    echo "</div>\n";
    
    // Direct preview link
    echo "<div style='background-color: #e3f2fd; padding: 15px; border: 1px solid #2196f3; border-radius: 4px; margin: 20px 0;'>\n";
    echo "<h3>🔗 Direct Preview Links</h3>\n";
    echo "<p><a href='admin/preview_template.php?id=$template_id' target='_blank'>Full Preview Page</a></p>\n";
    echo "<p><a href='admin/preview_template.php?id=$template_id&embed=1' target='_blank'>Embedded Preview (iframe content)</a></p>\n";
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<p>❌ <strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>\n";
}
?>
