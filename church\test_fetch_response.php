<?php
session_start();
require_once 'config.php';

// Auto-login as admin
if (!isset($_SESSION['admin_id'])) {
    $stmt = $pdo->prepare("SELECT id, username, password FROM admins WHERE username = ?");
    $stmt->execute(['admin']);
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($admin && password_verify('admin123', $admin['password'])) {
        $_SESSION['admin_id'] = $admin['id'];
        $_SESSION['admin_username'] = $admin['username'];
    }
}

echo "<h2>Test Fetch Response</h2>";
echo "<p>✅ Logged in as admin (ID: {$_SESSION['admin_id']})</p>";
?>

<script>
// Test the exact same fetch request as the admin interface
function testFetch() {
    console.log('Testing fetch request...');
    
    // Create FormData exactly like the admin interface
    const formData = new FormData();
    formData.append('title', 'Test Fetch Event');
    formData.append('description', 'Testing fetch response');
    formData.append('start_datetime', '2025-07-20T10:00');
    formData.append('end_datetime', '2025-07-20T12:00');
    formData.append('location', 'Test Location');
    formData.append('capacity', '50');
    formData.append('status', 'published');
    formData.append('has_promotional_files', '1');
    
    // Create a small test file
    const canvas = document.createElement('canvas');
    canvas.width = 100;
    canvas.height = 50;
    const ctx = canvas.getContext('2d');
    ctx.fillStyle = 'blue';
    ctx.fillRect(0, 0, 100, 50);
    ctx.fillStyle = 'white';
    ctx.font = '16px Arial';
    ctx.fillText('TEST', 30, 30);
    
    canvas.toBlob(function(blob) {
        const file = new File([blob], 'test-image.png', { type: 'image/png' });
        formData.append('promotional_files[]', file);
        
        console.log('FormData prepared, making fetch request...');
        
        // Make the exact same fetch request
        fetch('admin/create_event_with_materials.php', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            console.log('Response received:', response);
            console.log('Response status:', response.status);
            console.log('Response statusText:', response.statusText);
            console.log('Response ok:', response.ok);
            console.log('Response headers:', response.headers);
            
            // Log response headers
            for (let [key, value] of response.headers.entries()) {
                console.log(`Header ${key}: ${value}`);
            }
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status} - ${response.statusText}`);
            }
            
            return response.text();
        })
        .then(text => {
            console.log('Raw response text:', text);
            console.log('Response length:', text.length);
            
            document.getElementById('response-output').innerHTML = 
                '<h3>Response Details:</h3>' +
                '<p><strong>Status:</strong> Success</p>' +
                '<p><strong>Response Length:</strong> ' + text.length + ' characters</p>' +
                '<h4>Raw Response:</h4>' +
                '<pre>' + text.substring(0, 1000) + (text.length > 1000 ? '...' : '') + '</pre>';
            
            try {
                const data = JSON.parse(text);
                console.log('Parsed JSON:', data);
                document.getElementById('json-output').innerHTML = 
                    '<h4>Parsed JSON:</h4>' +
                    '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (e) {
                console.error('JSON parse error:', e);
                document.getElementById('json-output').innerHTML = 
                    '<h4>JSON Parse Error:</h4>' +
                    '<p style="color: red;">' + e.message + '</p>';
            }
        })
        .catch(error => {
            console.error('Fetch error:', error);
            console.error('Error stack:', error.stack);
            
            document.getElementById('response-output').innerHTML = 
                '<h3>Fetch Error:</h3>' +
                '<p style="color: red;"><strong>Error:</strong> ' + error.message + '</p>' +
                '<p><strong>Error Type:</strong> ' + error.name + '</p>';
        });
    }, 'image/png');
}
</script>

<h3>Test Fetch Request</h3>
<p>This will test the exact same fetch request that the admin interface makes.</p>
<button onclick="testFetch()">Test Fetch Request</button>

<div id="response-output"></div>
<div id="json-output"></div>

<h3>Instructions</h3>
<ol>
    <li>Click "Test Fetch Request" above</li>
    <li>Open browser console (F12 → Console tab)</li>
    <li>Look at the detailed logs to see what's happening</li>
    <li>Check the response details below</li>
</ol>

<h3>Common "Failed to fetch" Causes</h3>
<ul>
    <li><strong>CORS issues:</strong> Cross-origin request blocked</li>
    <li><strong>Network error:</strong> Server not responding</li>
    <li><strong>HTTP error:</strong> 500, 404, etc.</li>
    <li><strong>PHP fatal error:</strong> Script crashes before response</li>
    <li><strong>File upload limits:</strong> Request too large</li>
</ul>
