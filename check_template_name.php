<?php
/**
 * Check template name for ID 46
 */

require_once 'config.php';

echo "<h2>Check Template Name for ID 46</h2>\n";

try {
    // Get template 46 specifically
    $stmt = $pdo->prepare("SELECT id, template_name, is_birthday_template FROM email_templates WHERE id = 46");
    $stmt->execute();
    $template = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$template) {
        echo "<p>❌ Template ID 46 not found!</p>\n";
        exit;
    }
    
    echo "<div style='border: 2px solid #007bff; margin: 20px 0; padding: 15px;'>\n";
    echo "<h3>Template Details</h3>\n";
    echo "<p><strong>ID:</strong> {$template['id']}</p>\n";
    echo "<p><strong>Name:</strong> " . htmlspecialchars($template['template_name']) . "</p>\n";
    echo "<p><strong>Is Birthday Template:</strong> " . ($template['is_birthday_template'] ? 'Yes' : 'No') . "</p>\n";
    
    // Check if name contains "notification"
    $hasNotification = strpos(strtolower($template['template_name']), 'notification') !== false;
    echo "<p><strong>Contains 'notification':</strong> " . ($hasNotification ? 'Yes' : 'No') . "</p>\n";
    
    if (!$hasNotification) {
        echo "<div style='background-color: #fff3cd; color: #856404; padding: 15px; border: 1px solid #ffeaa7; border-radius: 4px; margin: 10px 0;'>\n";
        echo "<h4>⚠️ FOUND THE ISSUE!</h4>\n";
        echo "<p>The template name doesn't contain 'notification', so the preview system won't apply birthday member image replacements.</p>\n";
        echo "<p>We need to either:</p>\n";
        echo "<ol>\n";
        echo "<li>Update the template name to include 'notification', OR</li>\n";
        echo "<li>Fix the preview system to check for birthday templates differently</li>\n";
        echo "</ol>\n";
        echo "</div>\n";
        
        // Option 1: Update template name
        echo "<h4>Option 1: Update Template Name</h4>\n";
        $newName = $template['template_name'] . ' Notification';
        echo "<p>Suggested new name: <strong>" . htmlspecialchars($newName) . "</strong></p>\n";
        
        $updateStmt = $pdo->prepare("UPDATE email_templates SET template_name = ? WHERE id = 46");
        $result = $updateStmt->execute([$newName]);
        
        if ($result) {
            echo "<p style='color: green;'>✅ <strong>Template name updated successfully!</strong></p>\n";
        } else {
            echo "<p style='color: red;'>❌ <strong>Failed to update template name!</strong></p>\n";
        }
    } else {
        echo "<p style='color: green;'>✅ Template name is correct for birthday notifications</p>\n";
    }
    
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<p>❌ <strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
}
?>
