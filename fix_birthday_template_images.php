<?php
/**
 * Fix script to correct birthday template image issues
 */

require_once 'config.php';

echo "<h2>Birthday Template Image Fix</h2>\n";

try {
    // Step 1: Check and fix templates in database
    echo "<h3>Step 1: Checking Database Templates</h3>\n";
    
    $stmt = $pdo->prepare("
        SELECT id, template_name, content
        FROM email_templates 
        WHERE is_birthday_template = 1
        ORDER BY template_name
    ");
    $stmt->execute();
    $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $fixedTemplates = 0;
    
    foreach ($templates as $template) {
        echo "<h4>Checking: " . htmlspecialchars($template['template_name']) . "</h4>\n";
        
        $content = $template['content'];
        $originalContent = $content;
        $hasChanges = false;
        
        // Check for hardcoded image URLs
        preg_match_all('/<img[^>]+src=["\']([^"\']+)["\'][^>]*>/i', $content, $imgMatches);
        
        if (!empty($imgMatches[1])) {
            echo "<p>Found " . count($imgMatches[1]) . " image(s) in template:</p>\n";
            echo "<ul>\n";
            
            foreach ($imgMatches[1] as $index => $srcUrl) {
                echo "<li><strong>Image " . ($index + 1) . ":</strong> " . htmlspecialchars($srcUrl) . "\n";
                
                // Check if this is a hardcoded URL that should be a placeholder
                if (strpos($srcUrl, 'localhost') !== false || 
                    strpos($srcUrl, 'uploads/') !== false || 
                    preg_match('/uploads\/\d+/', $srcUrl)) {
                    
                    echo " <span style='color: red;'>❌ HARDCODED - NEEDS FIX</span>\n";
                    
                    // Replace with proper placeholder
                    $newSrc = '{birthday_member_image}';
                    $content = str_replace($srcUrl, $newSrc, $content);
                    $hasChanges = true;
                    
                    echo "<br>→ Will replace with: <code>$newSrc</code>\n";
                } elseif (strpos($srcUrl, '{') !== false && strpos($srcUrl, '}') !== false) {
                    echo " <span style='color: green;'>✅ PLACEHOLDER - OK</span>\n";
                } else {
                    echo " <span style='color: orange;'>⚠️ UNKNOWN TYPE</span>\n";
                }
                echo "</li>\n";
            }
            echo "</ul>\n";
        } else {
            echo "<p>ℹ️ No images found in this template.</p>\n";
        }
        
        // Update the template if changes were made
        if ($hasChanges) {
            echo "<p><strong>🔧 Updating template in database...</strong></p>\n";
            
            $updateStmt = $pdo->prepare("UPDATE email_templates SET content = ? WHERE id = ?");
            $updateStmt->execute([$content, $template['id']]);
            
            echo "<p>✅ Template updated successfully!</p>\n";
            $fixedTemplates++;
            
            echo "<h5>Before:</h5>\n";
            echo "<pre style='background-color: #ffe6e6; padding: 10px; border: 1px solid #ff9999;'>\n";
            echo htmlspecialchars($originalContent);
            echo "</pre>\n";
            
            echo "<h5>After:</h5>\n";
            echo "<pre style='background-color: #e6ffe6; padding: 10px; border: 1px solid #99ff99;'>\n";
            echo htmlspecialchars($content);
            echo "</pre>\n";
        } else {
            echo "<p>✅ No changes needed for this template.</p>\n";
        }
        
        echo "<hr>\n";
    }
    
    echo "<h3>Step 2: Database Template Fix Summary</h3>\n";
    echo "<p><strong>Templates processed:</strong> " . count($templates) . "</p>\n";
    echo "<p><strong>Templates fixed:</strong> $fixedTemplates</p>\n";
    
    // Step 2: Test template processing
    echo "<h3>Step 3: Testing Template Processing</h3>\n";
    
    // Get the first birthday template for testing
    $stmt = $pdo->prepare("
        SELECT id, template_name, content
        FROM email_templates 
        WHERE is_birthday_template = 1
        ORDER BY template_name
        LIMIT 1
    ");
    $stmt->execute();
    $testTemplate = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($testTemplate) {
        echo "<h4>Testing Template: " . htmlspecialchars($testTemplate['template_name']) . "</h4>\n";
        
        // Create test member data
        $testMemberData = [
            'first_name' => 'John',
            'full_name' => 'John Doe',
            'birthday_member_first_name' => 'Ndivhuwo',
            'birthday_member_full_name' => 'Ndivhuwo Machiba',
            'birthday_member_age' => 40,
            'birthday_member_image' => 'http://localhost/campaign/church/uploads/profiles/ndivhuwo.jpg',
            'birthday_member_photo_url' => 'http://localhost/campaign/church/uploads/profiles/ndivhuwo.jpg',
            'member_image' => 'http://localhost/campaign/church/uploads/profiles/ndivhuwo.jpg',
            'member_image_url' => 'http://localhost/campaign/church/uploads/profiles/ndivhuwo.jpg',
            'image_path' => 'http://localhost/campaign/church/uploads/profiles/ndivhuwo.jpg',
            'profile_photo' => 'http://localhost/campaign/church/uploads/profiles/ndivhuwo.jpg',
            '_original_image_path' => 'uploads/profiles/ndivhuwo.jpg',
            '_is_birthday_notification' => true
        ];
        
        // Process the template
        $processedContent = replaceTemplatePlaceholders($testTemplate['content'], $testMemberData);
        
        echo "<h5>Template Content After Processing:</h5>\n";
        echo "<pre style='background-color: #f0f8ff; padding: 10px; border: 1px solid #007bff;'>\n";
        echo htmlspecialchars($processedContent);
        echo "</pre>\n";
        
        // Check the processed content for image URLs
        preg_match_all('/<img[^>]+src=["\']([^"\']+)["\'][^>]*>/i', $processedContent, $processedImgMatches);
        
        if (!empty($processedImgMatches[1])) {
            echo "<h5>Images in Processed Content:</h5>\n";
            echo "<ul>\n";
            foreach ($processedImgMatches[1] as $srcUrl) {
                echo "<li><strong>" . htmlspecialchars($srcUrl) . "</strong>\n";
                
                if (strpos($srcUrl, 'ndivhuwo.jpg') !== false) {
                    echo " <span style='color: green;'>✅ CORRECT MEMBER IMAGE</span>\n";
                } elseif (strpos($srcUrl, '{') !== false) {
                    echo " <span style='color: red;'>❌ UNPROCESSED PLACEHOLDER</span>\n";
                } else {
                    echo " <span style='color: orange;'>⚠️ OTHER IMAGE</span>\n";
                }
                echo "</li>\n";
            }
            echo "</ul>\n";
        }
        
        echo "<h5>Visual Preview:</h5>\n";
        echo "<div style='border: 2px solid #28a745; padding: 15px; background-color: #f8f9fa;'>\n";
        echo $processedContent;
        echo "</div>\n";
    }
    
    echo "<h3>Step 4: Summary</h3>\n";
    echo "<div style='background-color: #d4edda; color: #155724; padding: 15px; border: 1px solid #c3e6cb; border-radius: 4px;'>\n";
    echo "<h4>✅ Birthday Template Image Fix Applied</h4>\n";
    echo "<ul>\n";
    echo "<li><strong>Database Templates Fixed:</strong> $fixedTemplates templates updated</li>\n";
    echo "<li><strong>Hardcoded URLs Replaced:</strong> With dynamic {birthday_member_image} placeholder</li>\n";
    echo "<li><strong>Template Processing:</strong> Verified to work correctly</li>\n";
    echo "</ul>\n";
    echo "<p><strong>Next Steps:</strong></p>\n";
    echo "<ol>\n";
    echo "<li>Fix the preview template file (admin/preview_template.php)</li>\n";
    echo "<li>Test the birthday notification sending</li>\n";
    echo "<li>Verify images appear inline in emails</li>\n";
    echo "</ol>\n";
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<p>❌ <strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>\n";
}
?>
