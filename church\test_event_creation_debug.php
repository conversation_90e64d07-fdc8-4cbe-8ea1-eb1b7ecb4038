<?php
session_start();
require_once 'config.php';

echo "<h2>Event Creation Debug Test</h2>";

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    echo "<p>❌ Admin not logged in. Please <a href='admin/login.php'>login first</a>, then come back to this page.</p>";
    exit;
}

echo "<p>✅ Admin logged in: ID=" . $_SESSION['admin_id'] . "</p>";

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<h3>Simulating AJAX Event Creation...</h3>";
    
    // Simulate the AJAX request to create_event_with_materials.php
    $postData = [
        'title' => $_POST['title'],
        'description' => $_POST['description'],
        'start_datetime' => $_POST['start_datetime'],
        'end_datetime' => $_POST['end_datetime'],
        'location' => $_POST['location'],
        'capacity' => $_POST['capacity'],
        'status' => $_POST['status']
    ];
    
    echo "<p><strong>POST Data being sent:</strong></p>";
    echo "<pre>" . print_r($postData, true) . "</pre>";
    
    // Use cURL to make the request
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost/campaign/church/admin/create_event_with_materials.php');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    
    // Include session cookies
    $cookie_file = tempnam(sys_get_temp_dir(), 'cookie');
    curl_setopt($ch, CURLOPT_COOKIEJAR, $cookie_file);
    curl_setopt($ch, CURLOPT_COOKIEFILE, $cookie_file);
    
    // Set session cookie manually
    $session_name = session_name();
    $session_id = session_id();
    curl_setopt($ch, CURLOPT_COOKIE, "$session_name=$session_id");
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    echo "<p><strong>HTTP Response Code:</strong> $http_code</p>";
    if ($error) {
        echo "<p><strong>cURL Error:</strong> $error</p>";
    }
    echo "<p><strong>Response:</strong></p>";
    echo "<pre>" . htmlspecialchars($response) . "</pre>";
    
    // Clean up
    unlink($cookie_file);
    
    // Also check PHP error log
    $error_log = ini_get('error_log');
    if ($error_log && file_exists($error_log)) {
        echo "<h3>Recent PHP Error Log Entries:</h3>";
        $log_lines = file($error_log);
        $recent_lines = array_slice($log_lines, -20); // Last 20 lines
        echo "<pre>" . htmlspecialchars(implode('', $recent_lines)) . "</pre>";
    }
    
} else {
    // Show form
    ?>
    <form method="POST">
        <h3>Create Test Event</h3>
        <p>Title: <input type="text" name="title" value="Debug Test Event" required></p>
        <p>Description: <textarea name="description">Debug test description</textarea></p>
        <p>Start Date/Time: <input type="datetime-local" name="start_datetime" value="2025-07-20T10:00" required></p>
        <p>End Date/Time: <input type="datetime-local" name="end_datetime" value="2025-07-20T12:00" required></p>
        <p>Location: <input type="text" name="location" value="Debug Location"></p>
        <p>Capacity: <input type="number" name="capacity" value="50"></p>
        <p>Status: 
            <select name="status">
                <option value="published">Published</option>
                <option value="draft">Draft</option>
            </select>
        </p>
        <p><button type="submit">Test Event Creation</button></p>
    </form>
    <?php
}
?>
