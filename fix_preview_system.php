<?php
/**
 * Fix the preview system for birthday templates
 */

require_once 'config.php';

echo "<h2>Fix Preview System for Birthday Templates</h2>\n";

try {
    // Create a special override for the preview system
    echo "<div style='background-color: #e3f2fd; padding: 15px; border: 1px solid #2196f3; border-radius: 4px; margin: 20px 0;'>\n";
    echo "<h3>🔧 Creating Preview System Override</h3>\n";
    
    // Create a special version of replaceTemplatePlaceholders for preview
    $overrideCode = '<?php
/**
 * Special override for preview system
 */

// Override the replaceTemplatePlaceholders function for preview
function replaceTemplatePlaceholders_preview($content, $memberData, $skipMemberImage = false) {
    // Call the original function
    $result = replaceTemplatePlaceholders($content, $memberData, $skipMemberImage);
    
    // Special handling for birthday_member_image in preview context
    if (isset($memberData["birthday_member_image"]) && strpos($result, "http") !== false) {
        // Replace any full URLs with the relative path for preview
        $result = str_replace($memberData["birthday_member_image"], "../" . ltrim($memberData["image_path"], "/"), $result);
    }
    
    return $result;
}

// Create a special version of the preview_template.php file
$previewTemplateContent = file_get_contents("admin/preview_template.php");

// Replace the function call with our special version
$previewTemplateContent = str_replace(
    "replaceTemplatePlaceholders(",
    "replaceTemplatePlaceholders_preview(",
    $previewTemplateContent
);

// Save the modified file
file_put_contents("admin/preview_template_fixed.php", $previewTemplateContent);

echo "Preview system override created successfully!";
';
    
    // Save the override file
    file_put_contents('preview_override.php', $overrideCode);
    echo "<p>✅ Created preview system override file</p>\n";
    
    // Include the override
    include 'preview_override.php';
    echo "<p>✅ Applied preview system override</p>\n";
    
    echo "</div>\n";
    
    // Create a direct fix for template 46
    echo "<h3>Direct Fix for Template 46</h3>\n";
    
    // Get template 46
    $stmt = $pdo->prepare("SELECT * FROM email_templates WHERE id = 46");
    $stmt->execute();
    $template = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$template) {
        echo "<p>❌ Template ID 46 not found!</p>\n";
    } else {
        echo "<div style='border: 1px solid #28a745; margin: 10px 0; padding: 10px; background-color: #f8fff8;'>\n";
        echo "<h4>" . htmlspecialchars($template['template_name']) . "</h4>\n";
        
        // Check if template has hardcoded URLs
        $hasHardcodedUrls = strpos($template['content'], 'localhost') !== false || 
                           preg_match('/uploads\/[a-zA-Z0-9]+\.(png|jpg|jpeg|gif)/', $template['content']);
        
        if ($hasHardcodedUrls) {
            echo "<p>❌ Template still has hardcoded URLs</p>\n";
            
            // Fix the template content
            $fixedContent = preg_replace(
                [
                    '/http:\/\/localhost\/[^"\']*uploads\/[^"\']*\.(png|jpg|jpeg|gif)/i',
                    '/uploads\/[a-zA-Z0-9]+\.(png|jpg|jpeg|gif)/i',
                    '/<img([^>]+)src=["\'][^"\']*(?:localhost|uploads)[^"\']*["\']([^>]*)>/i'
                ],
                [
                    '{birthday_member_image}',
                    '{birthday_member_image}',
                    '<img$1src="{birthday_member_image}"$2>'
                ],
                $template['content']
            );
            
            // Update the database
            $updateStmt = $pdo->prepare("UPDATE email_templates SET content = ?, is_birthday_template = 1 WHERE id = 46");
            $result = $updateStmt->execute([$fixedContent]);
            
            if ($result) {
                echo "<p style='color: green;'>✅ <strong>Template 46 updated successfully!</strong></p>\n";
            } else {
                echo "<p style='color: red;'>❌ <strong>Failed to update template 46!</strong></p>\n";
            }
        } else {
            echo "<p style='color: green;'>✅ Template content is already correct</p>\n";
        }
        
        // Make sure it's marked as a birthday template
        if (!$template['is_birthday_template']) {
            $updateStmt = $pdo->prepare("UPDATE email_templates SET is_birthday_template = 1 WHERE id = 46");
            $result = $updateStmt->execute();
            
            if ($result) {
                echo "<p style='color: green;'>✅ <strong>Template 46 marked as birthday template!</strong></p>\n";
            } else {
                echo "<p style='color: red;'>❌ <strong>Failed to mark template 46 as birthday template!</strong></p>\n";
            }
        }
        
        echo "</div>\n";
    }
    
    // Create a direct preview link
    echo "<div style='background-color: #cce5ff; padding: 15px; border: 1px solid #99ccff; border-radius: 4px; margin: 20px 0;'>\n";
    echo "<h3>🔗 Fixed Preview Links</h3>\n";
    echo "<p><a href='admin/preview_template_fixed.php?id=46' target='_blank'>Fixed Preview for Template 46</a></p>\n";
    echo "<p><a href='admin/preview_template_fixed.php?id=46&embed=1' target='_blank'>Fixed Embedded Preview for Template 46</a></p>\n";
    echo "</div>\n";
    
    // Final instructions
    echo "<div style='background-color: #d4edda; color: #155724; padding: 15px; border: 1px solid #c3e6cb; border-radius: 4px; margin: 20px 0;'>\n";
    echo "<h3>🎉 Final Instructions</h3>\n";
    echo "<ol>\n";
    echo "<li><strong>Clear browser cache</strong> (Ctrl+Shift+R)</li>\n";
    echo "<li><strong>Go back to the admin birthday notification page</strong></li>\n";
    echo "<li><strong>Refresh the page</strong> (F5 or Ctrl+R)</li>\n";
    echo "<li><strong>Click on Ndivhuwo Machiba's birthday notification</strong></li>\n";
    echo "<li><strong>Select the template from the dropdown</strong></li>\n";
    echo "<li><strong>Check the preview</strong> - it should now show the actual member image</li>\n";
    echo "</ol>\n";
    echo "<p>If the image still doesn't show, try using the fixed preview link above.</p>\n";
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<p>❌ <strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>\n";
}
?>
