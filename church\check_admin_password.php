<?php
require_once 'config.php';

echo "<h2>Admin Password Check</h2>";

try {
    $stmt = $pdo->prepare("SELECT id, username, password FROM admins WHERE username = ?");
    $stmt->execute(['admin']);
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($admin) {
        echo "<p>✅ Admin user found:</p>";
        echo "<ul>";
        echo "<li>ID: " . $admin['id'] . "</li>";
        echo "<li>Username: " . $admin['username'] . "</li>";
        echo "<li>Password Hash: " . $admin['password'] . "</li>";
        echo "</ul>";
        
        // Test password verification
        $test_passwords = ['admin123', 'admin', 'password', '123456'];
        echo "<h3>Password Testing:</h3>";
        foreach ($test_passwords as $test_pass) {
            $is_valid = password_verify($test_pass, $admin['password']);
            echo "<p>Password '$test_pass': " . ($is_valid ? "✅ VALID" : "❌ Invalid") . "</p>";
        }
        
        // Show how to create a new password hash
        echo "<h3>Create New Password Hash:</h3>";
        $new_hash = password_hash('admin123', PASSWORD_DEFAULT);
        echo "<p>New hash for 'admin123': $new_hash</p>";
        
        // Update the password if needed
        if (isset($_GET['update_password'])) {
            $stmt = $pdo->prepare("UPDATE admin_users SET password = ? WHERE username = ?");
            $result = $stmt->execute([$new_hash, 'admin']);
            if ($result) {
                echo "<p>✅ Password updated successfully!</p>";
            } else {
                echo "<p>❌ Failed to update password</p>";
            }
        } else {
            echo "<p><a href='?update_password=1'>Click here to update admin password to 'admin123'</a></p>";
        }
        
    } else {
        echo "<p>❌ Admin user not found</p>";
        
        // Create admin user
        echo "<h3>Creating Admin User:</h3>";
        $password_hash = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT INTO admin_users (username, password, email, full_name, role, is_active) VALUES (?, ?, ?, ?, ?, ?)");
        $result = $stmt->execute(['admin', $password_hash, '<EMAIL>', 'Administrator', 'super_admin', 1]);
        
        if ($result) {
            echo "<p>✅ Admin user created successfully!</p>";
            echo "<p>Username: admin</p>";
            echo "<p>Password: admin123</p>";
        } else {
            echo "<p>❌ Failed to create admin user</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}
?>
