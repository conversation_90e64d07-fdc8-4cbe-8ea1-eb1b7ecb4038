<?php
/**
 * Create Event with Promotional Materials
 * 
 * Handles event creation and promotional material uploads in a single atomic transaction
 */

session_start();

// Include configuration
require_once '../config.php';

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit();
}

try {
    // Start transaction
    $pdo->beginTransaction();
    
    // Validate required fields (matching the form requirements)
    $required_fields = ['title', 'start_datetime', 'end_datetime'];
    foreach ($required_fields as $field) {
        if (empty($_POST[$field])) {
            throw new Exception("Field '$field' is required");
        }
    }
    
    // Create the event first
    $stmt = $pdo->prepare("
        INSERT INTO events (title, description, event_date, location,
                          max_attendees, category_id, created_by, is_active)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    $stmt->execute([
        $_POST['title'],
        !empty($_POST['description']) ? $_POST['description'] : null,
        $_POST['start_datetime'],
        !empty($_POST['location']) ? $_POST['location'] : null,
        !empty($_POST['capacity']) ? (int)$_POST['capacity'] : null,
        !empty($_POST['category_id']) ? (int)$_POST['category_id'] : null,
        $_SESSION['admin_id'],
        ($_POST['status'] === 'published') ? 1 : 0
    ]);
    
    $event_id = $pdo->lastInsertId();
    
    // Handle promotional material uploads if any files were provided
    $uploaded_files = [];
    $upload_errors = [];
    
    if (!empty($_FILES['promotional_files']['name'][0])) {
        // Set up upload directories
        $base_dir = '../uploads/events/';
        $promotional_dir = $base_dir . 'promotional/';
        $thumbnails_dir = $base_dir . 'thumbnails/';
        
        // Create directories if they don't exist
        foreach ([$base_dir, $promotional_dir, $thumbnails_dir] as $dir) {
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }
        }
        
        // Process each uploaded file
        $file_count = count($_FILES['promotional_files']['name']);
        for ($i = 0; $i < $file_count; $i++) {
            if ($_FILES['promotional_files']['error'][$i] === UPLOAD_ERR_OK) {
                try {
                    $file_name = $_FILES['promotional_files']['name'][$i];
                    $file_tmp = $_FILES['promotional_files']['tmp_name'][$i];
                    $file_size = $_FILES['promotional_files']['size'][$i];
                    $file_type = $_FILES['promotional_files']['type'][$i];
                    
                    // Validate file
                    $allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'];
                    if (!in_array($file_type, $allowed_types)) {
                        throw new Exception("Invalid file type for $file_name");
                    }
                    
                    if ($file_size > 15 * 1024 * 1024) { // 15MB limit
                        throw new Exception("File $file_name is too large (max 15MB)");
                    }
                    
                    // Generate safe filename
                    $file_extension = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));
                    $safe_filename = preg_replace('/[^a-zA-Z0-9._-]/', '_', pathinfo($file_name, PATHINFO_FILENAME));
                    $safe_filename = preg_replace('/_+/', '_', $safe_filename);
                    $safe_filename = trim($safe_filename, '_');
                    $unique_name = 'event_' . $event_id . '_' . time() . '_' . $i . '_' . $safe_filename . '.' . $file_extension;
                    
                    // Determine file category and target directory
                    $file_category = in_array($file_extension, ['jpg', 'jpeg', 'png', 'gif']) ? 'promotional' : 'document';
                    $target_dir = ($file_category === 'promotional') ? $promotional_dir : $base_dir;
                    $file_path = $target_dir . $unique_name;
                    
                    // Move uploaded file
                    if (!move_uploaded_file($file_tmp, $file_path)) {
                        throw new Exception("Failed to save $file_name");
                    }
                    
                    // Generate thumbnail for images
                    $thumbnail_path = null;
                    if ($file_category === 'promotional') {
                        $thumbnail_path = generateThumbnail($file_path, $thumbnails_dir, $unique_name);
                    }
                    
                    // Check if this should be the header banner (first image uploaded)
                    $is_header_banner = ($file_category === 'promotional' && empty($uploaded_files));
                    
                    // Save to database
                    $file_stmt = $pdo->prepare("
                        INSERT INTO event_files (event_id, file_name, file_path, file_type, file_size,
                                               uploaded_by, file_category, is_header_banner, display_order)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ");
                    
                    $file_stmt->execute([
                        $event_id,
                        $file_name,
                        $file_path,
                        $file_type,
                        $file_size,
                        $_SESSION['admin_id'],
                        $file_category,
                        $is_header_banner ? 1 : 0,
                        $i + 1
                    ]);
                    
                    $uploaded_files[] = [
                        'id' => $pdo->lastInsertId(),
                        'name' => $file_name,
                        'category' => $file_category,
                        'is_header_banner' => $is_header_banner,
                        'thumbnail' => $thumbnail_path
                    ];
                    
                } catch (Exception $e) {
                    $upload_errors[] = "Error uploading $file_name: " . $e->getMessage();
                    
                    // Clean up file if it was moved
                    if (isset($file_path) && file_exists($file_path)) {
                        unlink($file_path);
                    }
                }
            } else if ($_FILES['promotional_files']['error'][$i] !== UPLOAD_ERR_NO_FILE) {
                $upload_errors[] = "Upload error for file " . ($_FILES['promotional_files']['name'][$i] ?? 'unknown');
            }
        }
    }
    
    // Commit transaction
    $pdo->commit();
    
    // Prepare response
    $response = [
        'success' => true,
        'message' => 'Event created successfully',
        'event_id' => $event_id,
        'uploaded_files' => $uploaded_files,
        'upload_errors' => $upload_errors
    ];
    
    if (!empty($upload_errors)) {
        $response['message'] .= ' with some file upload issues';
    }
    
    echo json_encode($response);
    
} catch (Exception $e) {
    // Rollback transaction on error
    if ($pdo->inTransaction()) {
        $pdo->rollback();
    }
    
    // Clean up any uploaded files
    if (isset($uploaded_files)) {
        foreach ($uploaded_files as $file) {
            if (isset($file['path']) && file_exists($file['path'])) {
                unlink($file['path']);
            }
        }
    }
    
    error_log("Event creation with materials error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}

/**
 * Generate thumbnail for image files
 */
function generateThumbnail($source_path, $thumbnails_dir, $filename) {
    try {
        $thumbnail_path = $thumbnails_dir . 'thumb_' . $filename;
        
        // Get image info
        $image_info = getimagesize($source_path);
        if (!$image_info) {
            return null;
        }
        
        $width = $image_info[0];
        $height = $image_info[1];
        $type = $image_info[2];
        
        // Calculate thumbnail dimensions (max 200x200)
        $max_size = 200;
        if ($width > $height) {
            $new_width = $max_size;
            $new_height = ($height / $width) * $max_size;
        } else {
            $new_height = $max_size;
            $new_width = ($width / $height) * $max_size;
        }
        
        // Create source image
        switch ($type) {
            case IMAGETYPE_JPEG:
                $source = imagecreatefromjpeg($source_path);
                break;
            case IMAGETYPE_PNG:
                $source = imagecreatefrompng($source_path);
                break;
            case IMAGETYPE_GIF:
                $source = imagecreatefromgif($source_path);
                break;
            default:
                return null;
        }
        
        // Create thumbnail
        $thumbnail = imagecreatetruecolor($new_width, $new_height);
        
        // Preserve transparency for PNG and GIF
        if ($type == IMAGETYPE_PNG || $type == IMAGETYPE_GIF) {
            imagealphablending($thumbnail, false);
            imagesavealpha($thumbnail, true);
            $transparent = imagecolorallocatealpha($thumbnail, 255, 255, 255, 127);
            imagefilledrectangle($thumbnail, 0, 0, $new_width, $new_height, $transparent);
        }
        
        imagecopyresampled($thumbnail, $source, 0, 0, 0, 0, $new_width, $new_height, $width, $height);
        
        // Save thumbnail
        switch ($type) {
            case IMAGETYPE_JPEG:
                imagejpeg($thumbnail, $thumbnail_path, 85);
                break;
            case IMAGETYPE_PNG:
                imagepng($thumbnail, $thumbnail_path);
                break;
            case IMAGETYPE_GIF:
                imagegif($thumbnail, $thumbnail_path);
                break;
        }
        
        imagedestroy($source);
        imagedestroy($thumbnail);
        
        return $thumbnail_path;
        
    } catch (Exception $e) {
        error_log("Thumbnail generation error: " . $e->getMessage());
        return null;
    }
}
?>
