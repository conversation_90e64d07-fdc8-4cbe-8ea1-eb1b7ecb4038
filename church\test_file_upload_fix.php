<?php
session_start();
require_once 'config.php';

// Auto-login as admin
if (!isset($_SESSION['admin_id'])) {
    $stmt = $pdo->prepare("SELECT id, username, password FROM admins WHERE username = ?");
    $stmt->execute(['admin']);
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($admin && password_verify('admin123', $admin['password'])) {
        $_SESSION['admin_id'] = $admin['id'];
        $_SESSION['admin_username'] = $admin['username'];
    }
}

echo "<h2>File Upload Fix Test</h2>";
echo "<p>✅ Logged in as admin (ID: {$_SESSION['admin_id']})</p>";

// Create a small test image
function createTestImage($filename, $width = 100, $height = 50) {
    $image = imagecreate($width, $height);
    $white = imagecolorallocate($image, 255, 255, 255);
    $black = imagecolorallocate($image, 0, 0, 0);
    imagefill($image, 0, 0, $white);
    imagestring($image, 3, 10, 15, 'TEST', $black);
    imagejpeg($image, $filename, 90);
    imagedestroy($image);
    return filesize($filename);
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<h3>Testing Event Creation with File Upload</h3>";
    
    // Create a test image
    $test_image = 'test_upload.jpg';
    $image_size = createTestImage($test_image);
    echo "<p>Created test image: $test_image ($image_size bytes)</p>";
    
    // Simulate file upload
    $_FILES['promotional_files'] = [
        'name' => [$test_image],
        'type' => ['image/jpeg'],
        'tmp_name' => [$test_image], // Using the created file as temp
        'error' => [UPLOAD_ERR_OK],
        'size' => [$image_size]
    ];
    
    // Test the create_event_with_materials.php logic
    try {
        // Simulate POST data
        $_POST = [
            'title' => 'Test Event with File',
            'description' => 'Testing file upload',
            'start_datetime' => '2025-07-20T10:00',
            'end_datetime' => '2025-07-20T12:00',
            'location' => 'Test Location',
            'capacity' => '50',
            'status' => 'published'
        ];
        
        echo "<h4>Simulating AJAX Request to create_event_with_materials.php</h4>";
        
        // Use cURL to test the endpoint
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'http://localhost/campaign/church/admin/create_event_with_materials.php');
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        
        // Set session cookie
        $session_name = session_name();
        $session_id = session_id();
        curl_setopt($ch, CURLOPT_COOKIE, "$session_name=$session_id");
        
        // Prepare multipart form data
        $postData = [
            'title' => 'Test Event with File',
            'description' => 'Testing file upload',
            'start_datetime' => '2025-07-20T10:00',
            'end_datetime' => '2025-07-20T12:00',
            'location' => 'Test Location',
            'capacity' => '50',
            'status' => 'published',
            'promotional_files[]' => new CURLFile($test_image, 'image/jpeg', $test_image)
        ];
        
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        echo "<p><strong>HTTP Response Code:</strong> $http_code</p>";
        if ($error) {
            echo "<p><strong>cURL Error:</strong> $error</p>";
        }
        echo "<p><strong>Response:</strong></p>";
        echo "<pre>" . htmlspecialchars($response) . "</pre>";
        
        // Parse JSON response
        $result = json_decode($response, true);
        if ($result) {
            if ($result['success']) {
                echo "<p>✅ <strong>SUCCESS!</strong> Event created with file upload</p>";
                if (!empty($result['uploaded_files'])) {
                    echo "<p>✅ Files uploaded: " . count($result['uploaded_files']) . "</p>";
                }
                if (!empty($result['upload_errors'])) {
                    echo "<p>⚠️ Upload errors: " . implode(', ', $result['upload_errors']) . "</p>";
                }
            } else {
                echo "<p>❌ <strong>FAILED:</strong> " . $result['message'] . "</p>";
            }
        } else {
            echo "<p>❌ Invalid JSON response</p>";
        }
        
    } catch (Exception $e) {
        echo "<p>❌ Exception: " . $e->getMessage() . "</p>";
    } finally {
        // Clean up test image
        if (file_exists($test_image)) {
            unlink($test_image);
            echo "<p>✅ Test image cleaned up</p>";
        }
    }
    
} else {
    ?>
    <h3>Test Event Creation with File Upload</h3>
    <p>This will test the fixed file upload functionality by creating an event with a promotional material.</p>
    
    <form method="POST">
        <p><button type="submit">Run File Upload Test</button></p>
    </form>
    
    <h3>Current PHP Upload Settings</h3>
    <table border="1">
        <tr><th>Setting</th><th>Value</th></tr>
        <tr><td>upload_max_filesize</td><td><?php echo ini_get('upload_max_filesize'); ?></td></tr>
        <tr><td>post_max_size</td><td><?php echo ini_get('post_max_size'); ?></td></tr>
        <tr><td>max_file_uploads</td><td><?php echo ini_get('max_file_uploads'); ?></td></tr>
        <tr><td>memory_limit</td><td><?php echo ini_get('memory_limit'); ?></td></tr>
    </table>
    
    <h3>What the Fix Does</h3>
    <ul>
        <li>✅ Checks PHP upload limits dynamically instead of hardcoded 15MB</li>
        <li>✅ Provides specific error messages for different upload failures</li>
        <li>✅ Better debugging with detailed error logging</li>
        <li>✅ Validates file types more clearly</li>
        <li>✅ Handles file size limits properly</li>
    </ul>
    <?php
}
?>
