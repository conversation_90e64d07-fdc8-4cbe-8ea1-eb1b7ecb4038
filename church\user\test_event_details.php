<?php
// Simple test to check event details API
session_start();

// Mock authentication for testing
$_SESSION['user_id'] = 51; // Use an existing user ID

// Include the database connection
require_once '../config/database.php';

// Test event ID 4 (Summer Weekend) which should have documents and requirements
$eventId = 4;

// Make a request to get_event_details.php
$url = "http://localhost/campaign/church/user/get_event_details.php?id=" . $eventId;

// Initialize cURL
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_COOKIE, session_name() . '=' . session_id());
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "<h2>Event Details API Test</h2>";
echo "<p><strong>HTTP Code:</strong> $httpCode</p>";
echo "<p><strong>Response:</strong></p>";
echo "<pre>" . htmlspecialchars($response) . "</pre>";

// Try to decode JSON
$data = json_decode($response, true);
if ($data) {
    echo "<h3>Parsed Data:</h3>";
    echo "<pre>" . print_r($data, true) . "</pre>";
    
    if (isset($data['event']['requirements'])) {
        echo "<h3>Requirements Found:</h3>";
        echo "<p>" . htmlspecialchars($data['event']['requirements']) . "</p>";
    } else {
        echo "<h3>No Requirements Field Found</h3>";
    }
}
?>
