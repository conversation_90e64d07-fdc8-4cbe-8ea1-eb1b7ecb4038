<?php
require_once 'config.php';

echo "<h2>Admin Password Test</h2>";

try {
    $stmt = $pdo->prepare("SELECT id, username, password FROM admins WHERE username = ?");
    $stmt->execute(['admin']);
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($admin) {
        echo "<p>✅ Admin user found:</p>";
        echo "<ul>";
        echo "<li>ID: " . $admin['id'] . "</li>";
        echo "<li>Username: " . $admin['username'] . "</li>";
        echo "<li>Password Hash: " . substr($admin['password'], 0, 20) . "...</li>";
        echo "</ul>";
        
        // Test common passwords
        $test_passwords = ['admin123', 'admin', 'password', '123456', 'Password123', 'Admin123'];
        echo "<h3>Password Testing:</h3>";
        foreach ($test_passwords as $test_pass) {
            $is_valid = password_verify($test_pass, $admin['password']);
            echo "<p>Password '$test_pass': " . ($is_valid ? "✅ VALID" : "❌ Invalid") . "</p>";
            if ($is_valid) {
                echo "<p><strong>🎉 FOUND CORRECT PASSWORD: '$test_pass'</strong></p>";
                break;
            }
        }
        
        // If no password worked, create a new one
        if (!$is_valid) {
            echo "<h3>Setting New Password:</h3>";
            $new_password = 'admin123';
            $new_hash = password_hash($new_password, PASSWORD_DEFAULT);
            
            $stmt = $pdo->prepare("UPDATE admins SET password = ? WHERE username = ?");
            $result = $stmt->execute([$new_hash, 'admin']);
            
            if ($result) {
                echo "<p>✅ Password updated successfully!</p>";
                echo "<p><strong>New password: '$new_password'</strong></p>";
            } else {
                echo "<p>❌ Failed to update password</p>";
            }
        }
        
    } else {
        echo "<p>❌ Admin user not found</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}
?>
