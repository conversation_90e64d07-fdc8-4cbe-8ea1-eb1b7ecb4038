<?php
session_start();
require_once 'config.php';

// Simulate admin login
$_SESSION['admin_id'] = 1;

echo "<h2>Testing Event Creation Endpoints</h2>";

// Test 1: Test the regular events.php endpoint
echo "<h3>Test 1: Regular events.php endpoint</h3>";

$_POST = [
    'action' => 'create_event',
    'title' => 'Test Event Regular',
    'description' => 'Test Description',
    'start_datetime' => '2025-07-20 10:00:00',
    'end_datetime' => '2025-07-20 12:00:00',
    'location' => 'Test Location',
    'capacity' => '50',
    'category_id' => '',
    'status' => 'published'
];

try {
    // Simulate the events.php logic
    $stmt = $pdo->prepare("
        INSERT INTO events (title, description, event_date, location,
                          max_attendees, category_id, created_by, is_active)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    ");

    $result = $stmt->execute([
        $_POST['title'],
        $_POST['description'],
        $_POST['start_datetime'], // Use start_datetime as event_date
        $_POST['location'],
        !empty($_POST['capacity']) ? (int)$_POST['capacity'] : null,
        !empty($_POST['category_id']) ? (int)$_POST['category_id'] : null,
        $_SESSION['admin_id'],
        ($_POST['status'] === 'published') ? 1 : 0 // Convert status to is_active
    ]);

    if ($result) {
        $event_id = $pdo->lastInsertId();
        echo "SUCCESS: Event created with ID: $event_id<br>";
        
        // Clean up
        $pdo->prepare("DELETE FROM events WHERE id = ?")->execute([$event_id]);
        echo "Test event cleaned up.<br>";
    } else {
        echo "FAILED: Could not create event<br>";
    }
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "<br>";
}

// Test 2: Test the create_event_with_materials.php endpoint
echo "<h3>Test 2: create_event_with_materials.php endpoint</h3>";

// Reset POST data
$_POST = [
    'title' => 'Test Event With Materials',
    'description' => 'Test Description',
    'start_datetime' => '2025-07-20 10:00:00',
    'end_datetime' => '2025-07-20 12:00:00',
    'location' => 'Test Location',
    'capacity' => '50',
    'category_id' => '',
    'status' => 'published'
];

// Clear FILES array for this test
$_FILES = [];

try {
    // Start transaction
    $pdo->beginTransaction();
    
    // Validate required fields (matching the form requirements)
    $required_fields = ['title', 'start_datetime', 'end_datetime'];
    foreach ($required_fields as $field) {
        if (empty($_POST[$field])) {
            throw new Exception("Field '$field' is required");
        }
    }
    
    // Create the event first
    $stmt = $pdo->prepare("
        INSERT INTO events (title, description, event_date, location,
                          max_attendees, category_id, created_by, is_active)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    $result = $stmt->execute([
        $_POST['title'],
        !empty($_POST['description']) ? $_POST['description'] : null,
        $_POST['start_datetime'],
        !empty($_POST['location']) ? $_POST['location'] : null,
        !empty($_POST['capacity']) ? (int)$_POST['capacity'] : null,
        !empty($_POST['category_id']) ? (int)$_POST['category_id'] : null,
        $_SESSION['admin_id'],
        ($_POST['status'] === 'published') ? 1 : 0
    ]);
    
    $event_id = $pdo->lastInsertId();
    
    // Commit transaction
    $pdo->commit();
    
    echo "SUCCESS: Event created with ID: $event_id<br>";
    
    // Clean up
    $pdo->prepare("DELETE FROM events WHERE id = ?")->execute([$event_id]);
    echo "Test event cleaned up.<br>";
    
} catch (Exception $e) {
    // Rollback transaction on error
    if ($pdo->inTransaction()) {
        $pdo->rollback();
    }
    
    echo "ERROR: " . $e->getMessage() . "<br>";
}

// Test 3: Check session and authentication
echo "<h3>Test 3: Session and Authentication</h3>";
echo "Session ID: " . session_id() . "<br>";
echo "Admin ID in session: " . (isset($_SESSION['admin_id']) ? $_SESSION['admin_id'] : 'NOT SET') . "<br>";

// Test 4: Check if admin user exists
echo "<h3>Test 4: Admin User Check</h3>";
try {
    $stmt = $pdo->prepare("SELECT id, username FROM admin_users WHERE id = ?");
    $stmt->execute([1]);
    $admin = $stmt->fetch();
    
    if ($admin) {
        echo "Admin user found: ID=" . $admin['id'] . ", Username=" . $admin['username'] . "<br>";
    } else {
        echo "Admin user with ID=1 not found<br>";
        
        // Check all admin users
        $stmt = $pdo->query("SELECT id, username FROM admin_users");
        $admins = $stmt->fetchAll();
        echo "Available admin users:<br>";
        foreach ($admins as $admin) {
            echo "- ID=" . $admin['id'] . ", Username=" . $admin['username'] . "<br>";
        }
    }
} catch (Exception $e) {
    echo "ERROR checking admin users: " . $e->getMessage() . "<br>";
}

echo "<h3>Test Complete</h3>";
?>
