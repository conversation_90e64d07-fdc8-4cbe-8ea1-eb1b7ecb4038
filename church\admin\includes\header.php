<?php
// Include session manager for timeout handling
require_once __DIR__ . '/session-manager.php';

// Include language system
require_once __DIR__ . '/language.php';

// Include admin notification functions
require_once __DIR__ . '/admin_notification_functions.php';

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: " . admin_url_for('login.php'));
    exit();
}
?>
<!DOCTYPE html>
<html lang="<?php echo get_current_language(); ?>" dir="<?php echo get_language_direction(); ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="generator" content="<?php echo get_organization_name(); ?> Management System v1.0">
    <!-- Debug info for path troubleshooting -->
    <meta name="debug-site-url" content="<?php echo stripslashes(SITE_URL); ?>">
    <meta name="debug-admin-url" content="<?php echo stripslashes(ADMIN_URL); ?>">
    <meta name="debug-env" content="<?php echo $environment ?? 'unknown'; ?>">
    <!-- Favicon Support -->
    <?php
    $favicon = get_site_setting('favicon_logo', '');
    if (!empty($favicon)): ?>
        <link rel="icon" type="image/x-icon" href="<?php echo get_base_url() . '/' . htmlspecialchars($favicon); ?>">
        <link rel="shortcut icon" type="image/x-icon" href="<?php echo get_base_url() . '/' . htmlspecialchars($favicon); ?>">
    <?php else: ?>
        <link rel="icon" type="image/x-icon" href="<?php echo get_base_url(); ?>/assets/images/favicon.ico">
    <?php endif; ?>

    <title><?php echo isset($page_title) ? $page_title : 'Admin Panel'; ?> - <?php echo get_site_title(); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome Icons (primary) -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Bootstrap Icons (backup) -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <!-- Added cache-busting parameter to prevent caching issues -->
    <link rel="stylesheet" href="<?php echo ADMIN_URL; ?>/admin-css-proxy.php?t=<?php echo time(); ?>">
    <link rel="stylesheet" href="<?php echo ADMIN_URL; ?>/css/font-fix.css?t=<?php echo time(); ?>">
    <link rel="stylesheet" href="../css/promotional-materials.css?t=<?php echo time(); ?>">

    <!-- Custom Theme CSS -->
    <?php
    $customThemeFile = __DIR__ . '/../css/custom-theme.css';
    if (file_exists($customThemeFile)): ?>
        <link rel="stylesheet" href="<?php echo ADMIN_URL; ?>/css/custom-theme.css?t=<?php echo filemtime($customThemeFile); ?>">
    <?php endif; ?>

    <!-- Dark Mode CSS -->
    <link rel="stylesheet" href="<?php echo ADMIN_URL; ?>/css/dark-mode.css?t=<?php echo time(); ?>">

    <!-- Accessibility CSS -->
    <link rel="stylesheet" href="<?php echo ADMIN_URL; ?>/css/accessibility.css?t=<?php echo time(); ?>">

    <?php if (isset($extra_css)): ?>
        <?php echo $extra_css; ?>
    <?php endif; ?>
    
    <!-- Base URL for JavaScript -->
    <script>
        var BASE_URL = '<?php echo get_base_url(); ?>';
        var ADMIN_URL = '<?php echo get_admin_url(); ?>';
        var SITE_URL = '<?php echo SITE_URL; ?>';
    </script>

    <!-- Theme Manager -->
    <script src="<?php echo ADMIN_URL; ?>/js/theme-manager.js?t=<?php echo time(); ?>"></script>

    <!-- Accessibility Manager -->
    <script src="<?php echo ADMIN_URL; ?>/js/accessibility-manager.js?t=<?php echo time(); ?>"></script>

    <!-- Language Switcher -->
    <script src="<?php echo ADMIN_URL; ?>/js/language-switcher.js?t=<?php echo time(); ?>"></script>

    <!-- Admin Notifications -->
    <script>
        // Admin notification system
        let notificationCheckInterval;

        function updateNotificationBadge() {
            fetch('<?php echo ADMIN_URL; ?>/ajax/get_admin_notifications.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const badge = document.querySelector('.nav-link[href*="notifications.php"] .badge');
                        if (data.unread_count > 0) {
                            if (badge) {
                                badge.textContent = data.unread_count > 99 ? '99+' : data.unread_count;
                                badge.style.display = 'inline-block';
                            } else {
                                // Create badge if it doesn't exist
                                const notificationLink = document.querySelector('.nav-link[href*="notifications.php"]');
                                if (notificationLink) {
                                    const newBadge = document.createElement('span');
                                    newBadge.className = 'badge bg-danger rounded-pill position-absolute top-0 start-100 translate-middle';
                                    newBadge.style.fontSize = '0.7rem';
                                    newBadge.textContent = data.unread_count > 99 ? '99+' : data.unread_count;
                                    notificationLink.appendChild(newBadge);
                                }
                            }
                        } else {
                            if (badge) {
                                badge.style.display = 'none';
                            }
                        }
                    }
                })
                .catch(error => {
                    console.log('Notification check failed:', error);
                });
        }

        // Check for new notifications every 30 seconds
        document.addEventListener('DOMContentLoaded', function() {
            updateNotificationBadge(); // Initial check
            notificationCheckInterval = setInterval(updateNotificationBadge, 30000);
        });

        // Clear interval when page unloads
        window.addEventListener('beforeunload', function() {
            if (notificationCheckInterval) {
                clearInterval(notificationCheckInterval);
            }
        });
    </script>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Include the sidebar -->
            <?php include dirname(__FILE__) . '/sidebar.php'; ?>
            
            <!-- Main Content -->
            <div class="main-content">
                <?php if (isset($page_header)): ?>
                <div class="row mb-4">
                    <div class="col-md-12">
                        <h2><?php echo $page_header; ?></h2>
                        <?php if (isset($page_description)): ?>
                            <p><?php echo $page_description; ?></p>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>
                
                <?php if (isset($message) && !empty($message)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo htmlspecialchars($message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>
                
                <?php if (isset($error) && !empty($error)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo htmlspecialchars($error); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?> 