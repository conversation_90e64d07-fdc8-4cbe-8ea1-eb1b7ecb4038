<?php
/**
 * Test Event Creation with Promotional Materials
 * 
 * Simple test page to verify the new event creation workflow
 */

session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

require_once '../config.php';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Event Creation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3>Test Event Creation with Promotional Materials</h3>
                    </div>
                    <div class="card-body">
                        <form id="testEventForm" enctype="multipart/form-data">
                            <div class="mb-3">
                                <label for="title" class="form-label">Event Title</label>
                                <input type="text" class="form-control" id="title" name="title" value="Test Event" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="description" class="form-label">Description</label>
                                <textarea class="form-control" id="description" name="description" rows="3" required>This is a test event to verify promotional materials upload.</textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label for="start_datetime" class="form-label">Event Date & Time</label>
                                <input type="datetime-local" class="form-control" id="start_datetime" name="start_datetime" 
                                       value="<?php echo date('Y-m-d\TH:i', strtotime('+1 week')); ?>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="location" class="form-label">Location</label>
                                <input type="text" class="form-control" id="location" name="location" value="Test Location" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="capacity" class="form-label">Capacity</label>
                                <input type="number" class="form-control" id="capacity" name="capacity" value="50">
                            </div>
                            
                            <div class="mb-3">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-control" id="status" name="status">
                                    <option value="draft">Draft</option>
                                    <option value="published">Published</option>
                                </select>
                            </div>
                            
                            <div class="mb-4">
                                <h5>Promotional Materials</h5>
                                <div class="border-2 border-dashed rounded p-4 text-center" 
                                     style="border-color: #dee2e6; cursor: pointer;"
                                     onclick="document.getElementById('promotional_files').click()">
                                    <i class="bi bi-cloud-upload fs-1 text-primary mb-2"></i>
                                    <p class="mb-1">Click to upload or drag and drop</p>
                                    <small class="text-muted">Images (JPG, PNG, GIF) and PDFs • Max 15MB each</small>
                                </div>
                                <input type="file" class="form-control d-none" id="promotional_files"
                                       name="promotional_files[]" multiple
                                       accept=".jpg,.jpeg,.png,.gif,.pdf">
                                
                                <div id="fileList" class="mt-3"></div>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary" id="submitBtn">
                                    Create Event with Materials
                                </button>
                            </div>
                        </form>
                        
                        <div id="result" class="mt-4"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let selectedFiles = [];
        
        document.getElementById('promotional_files').addEventListener('change', function(e) {
            selectedFiles = Array.from(e.target.files);
            displayFiles();
        });
        
        function displayFiles() {
            const fileList = document.getElementById('fileList');
            if (selectedFiles.length === 0) {
                fileList.innerHTML = '';
                return;
            }
            
            let html = '<h6>Selected Files:</h6><ul class="list-group">';
            selectedFiles.forEach((file, index) => {
                html += `
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <i class="bi ${getFileIcon(file.type)} me-2"></i>
                            ${file.name}
                            <small class="text-muted ms-2">(${formatFileSize(file.size)})</small>
                        </div>
                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeFile(${index})">
                            <i class="bi bi-x"></i>
                        </button>
                    </li>
                `;
            });
            html += '</ul>';
            fileList.innerHTML = html;
        }
        
        function removeFile(index) {
            selectedFiles.splice(index, 1);
            
            // Update file input
            const dt = new DataTransfer();
            selectedFiles.forEach(file => dt.items.add(file));
            document.getElementById('promotional_files').files = dt.files;
            
            displayFiles();
        }
        
        function getFileIcon(fileType) {
            if (fileType.startsWith('image/')) {
                return 'bi-image';
            } else if (fileType === 'application/pdf') {
                return 'bi-file-pdf';
            } else {
                return 'bi-file-earmark';
            }
        }
        
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        document.getElementById('testEventForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submitBtn');
            const originalText = submitBtn.innerHTML;
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Creating...';
            
            const formData = new FormData(this);
            
            fetch('create_event_with_materials.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('result');
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="alert alert-success">
                            <h5>Success!</h5>
                            <p>Event created with ID: ${data.event_id}</p>
                            <p>Uploaded files: ${data.uploaded_files.length}</p>
                            ${data.upload_errors.length > 0 ? 
                                '<p class="text-warning">Upload errors: ' + data.upload_errors.join(', ') + '</p>' : 
                                ''
                            }
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <h5>Error!</h5>
                            <p>${data.message}</p>
                        </div>
                    `;
                }
                
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            })
            .catch(error => {
                console.error('Error:', error);
                document.getElementById('result').innerHTML = `
                    <div class="alert alert-danger">
                        <h5>Error!</h5>
                        <p>An error occurred while creating the event.</p>
                    </div>
                `;
                
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            });
        });
    </script>
</body>
</html>
