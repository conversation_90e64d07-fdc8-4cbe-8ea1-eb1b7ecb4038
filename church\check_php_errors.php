<?php
echo "<h2>PHP Error Log Check</h2>";

// Check PHP error log location
$error_log = ini_get('error_log');
echo "<p><strong>Error log location:</strong> " . ($error_log ?: 'Default system log') . "</p>";

// Check if error log file exists and is readable
if ($error_log && file_exists($error_log)) {
    echo "<p>✅ Error log file exists</p>";
    
    // Read last 50 lines of error log
    $lines = file($error_log);
    $recent_lines = array_slice($lines, -50);
    
    echo "<h3>Recent Error Log Entries (Last 50 lines):</h3>";
    echo "<pre style='background: #f0f0f0; padding: 10px; max-height: 400px; overflow-y: scroll;'>";
    foreach ($recent_lines as $line) {
        // Highlight lines containing our debug messages or event-related errors
        if (strpos($line, 'EVENT_CREATE_DEBUG') !== false || 
            strpos($line, 'event') !== false || 
            strpos($line, 'upload') !== false ||
            strpos($line, 'create_event_with_materials') !== false) {
            echo "<strong style='color: red;'>$line</strong>";
        } else {
            echo htmlspecialchars($line);
        }
    }
    echo "</pre>";
} else {
    echo "<p>❌ Error log file not found or not accessible</p>";
    
    // Try common error log locations
    $common_locations = [
        'C:\\xampp\\apache\\logs\\error.log',
        'C:\\xampp\\php\\logs\\php_error_log',
        '/var/log/apache2/error.log',
        '/var/log/php_errors.log'
    ];
    
    echo "<h3>Checking common error log locations:</h3>";
    foreach ($common_locations as $location) {
        if (file_exists($location)) {
            echo "<p>✅ Found: $location</p>";
            
            // Read last 20 lines
            $lines = file($location);
            $recent_lines = array_slice($lines, -20);
            
            echo "<h4>Recent entries from $location:</h4>";
            echo "<pre style='background: #f0f0f0; padding: 10px; max-height: 200px; overflow-y: scroll;'>";
            foreach ($recent_lines as $line) {
                if (strpos($line, 'EVENT_CREATE_DEBUG') !== false || 
                    strpos($line, 'event') !== false || 
                    strpos($line, 'upload') !== false) {
                    echo "<strong style='color: red;'>$line</strong>";
                } else {
                    echo htmlspecialchars($line);
                }
            }
            echo "</pre>";
            break;
        } else {
            echo "<p>❌ Not found: $location</p>";
        }
    }
}

// Also check for any recent debug messages in a custom log
echo "<h3>Custom Debug Log Check</h3>";
$custom_log = 'debug.log';
if (file_exists($custom_log)) {
    echo "<p>✅ Custom debug log found</p>";
    echo "<pre style='background: #f0f0f0; padding: 10px;'>";
    echo htmlspecialchars(file_get_contents($custom_log));
    echo "</pre>";
} else {
    echo "<p>No custom debug log found</p>";
}

// Show current PHP error reporting settings
echo "<h3>PHP Error Reporting Settings</h3>";
echo "<table border='1'>";
echo "<tr><th>Setting</th><th>Value</th></tr>";
echo "<tr><td>error_reporting</td><td>" . error_reporting() . "</td></tr>";
echo "<tr><td>display_errors</td><td>" . (ini_get('display_errors') ? 'On' : 'Off') . "</td></tr>";
echo "<tr><td>log_errors</td><td>" . (ini_get('log_errors') ? 'On' : 'Off') . "</td></tr>";
echo "<tr><td>error_log</td><td>" . (ini_get('error_log') ?: 'Default') . "</td></tr>";
echo "</table>";
?>
