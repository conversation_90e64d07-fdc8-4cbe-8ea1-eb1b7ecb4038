<?php
/**
 * Debug template ID 46 specifically
 */

require_once 'config.php';

echo "<h2>Debug Template ID 46</h2>\n";

try {
    // Get template 46 specifically
    $stmt = $pdo->prepare("SELECT * FROM email_templates WHERE id = 46");
    $stmt->execute();
    $template = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$template) {
        echo "<p>❌ Template ID 46 not found!</p>\n";
        exit;
    }
    
    echo "<div style='border: 2px solid #007bff; margin: 20px 0; padding: 15px;'>\n";
    echo "<h3>Template: " . htmlspecialchars($template['template_name']) . " (ID: {$template['id']})</h3>\n";
    echo "<p><strong>Is Birthday Template:</strong> " . ($template['is_birthday_template'] ? 'Yes' : 'No') . "</p>\n";
    
    echo "<h4>Current Content:</h4>\n";
    echo "<pre style='background-color: #f8f9fa; padding: 10px; border: 1px solid #dee2e6; max-height: 400px; overflow-y: auto; white-space: pre-wrap;'>\n";
    echo htmlspecialchars($template['content']);
    echo "</pre>\n";
    
    // Check for issues
    $issues = [];
    
    if (strpos($template['content'], 'localhost') !== false) {
        $issues[] = "❌ Contains 'localhost' references";
    }
    
    if (preg_match('/uploads\/[a-zA-Z0-9]+\.(png|jpg|jpeg|gif)/', $template['content'])) {
        $issues[] = "❌ Contains hardcoded upload paths";
    }
    
    if (strpos($template['content'], '{birthday_member_image}') === false) {
        $issues[] = "❌ Missing {birthday_member_image} placeholder";
    }
    
    if (empty($issues)) {
        echo "<p style='color: green;'>✅ Template appears to be correctly formatted</p>\n";
    } else {
        echo "<div style='color: red;'>\n";
        foreach ($issues as $issue) {
            echo "<p>$issue</p>\n";
        }
        echo "</div>\n";
    }
    
    // Now let's manually fix this specific template
    echo "<h3>Manual Fix for Template 46</h3>\n";
    
    $originalContent = $template['content'];
    $fixedContent = $originalContent;
    
    // Apply comprehensive fixes
    $patterns = [
        '/http:\/\/localhost\/[^"\']*uploads\/[^"\']*\.(png|jpg|jpeg|gif)/i',
        '/uploads\/[a-zA-Z0-9]+\.(png|jpg|jpeg|gif)/i',
        '/"[^"]*uploads\/[^"]*\.(png|jpg|jpeg|gif)"/i',
        "/'[^']*uploads\/[^']*\.(png|jpg|jpeg|gif)'/i"
    ];
    
    $hasChanges = false;
    foreach ($patterns as $pattern) {
        if (preg_match($pattern, $fixedContent)) {
            $fixedContent = preg_replace($pattern, '{birthday_member_image}', $fixedContent);
            $hasChanges = true;
        }
    }
    
    if ($hasChanges) {
        echo "<h4>Fixed Content:</h4>\n";
        echo "<pre style='background-color: #e6ffe6; padding: 10px; border: 1px solid #99ff99; max-height: 400px; overflow-y: auto; white-space: pre-wrap;'>\n";
        echo htmlspecialchars($fixedContent);
        echo "</pre>\n";
        
        // Update the database
        $updateStmt = $pdo->prepare("UPDATE email_templates SET content = ? WHERE id = 46");
        $result = $updateStmt->execute([$fixedContent]);
        
        if ($result) {
            echo "<p style='color: green;'>✅ <strong>Template 46 updated successfully!</strong></p>\n";
        } else {
            echo "<p style='color: red;'>❌ <strong>Failed to update template 46!</strong></p>\n";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ No changes needed or pattern not matched</p>\n";
        
        // Let's try a more aggressive approach
        echo "<h4>Aggressive Fix Attempt:</h4>\n";
        
        // Find any img tags and replace their src
        $aggressiveContent = preg_replace(
            '/<img([^>]+)src=["\'][^"\']*["\']([^>]*)>/i',
            '<img$1src="{birthday_member_image}"$2>',
            $originalContent
        );
        
        if ($aggressiveContent !== $originalContent) {
            echo "<pre style='background-color: #e6ffe6; padding: 10px; border: 1px solid #99ff99; max-height: 400px; overflow-y: auto; white-space: pre-wrap;'>\n";
            echo htmlspecialchars($aggressiveContent);
            echo "</pre>\n";
            
            $updateStmt = $pdo->prepare("UPDATE email_templates SET content = ? WHERE id = 46");
            $result = $updateStmt->execute([$aggressiveContent]);
            
            if ($result) {
                echo "<p style='color: green;'>✅ <strong>Template 46 aggressively updated!</strong></p>\n";
            } else {
                echo "<p style='color: red;'>❌ <strong>Failed to aggressively update template 46!</strong></p>\n";
            }
        } else {
            echo "<p style='color: red;'>❌ Aggressive fix also didn't find patterns to replace</p>\n";
        }
    }
    
    echo "</div>\n";
    
    // Test the preview system
    echo "<h3>Testing Preview System</h3>\n";
    
    // Get a test member
    $stmt = $pdo->prepare("SELECT * FROM members WHERE full_name LIKE '%Ndivhuwo%' LIMIT 1");
    $stmt->execute();
    $testMember = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($testMember) {
        echo "<div style='border: 1px solid #28a745; padding: 10px; background-color: #f8fff8;'>\n";
        echo "<h4>Test Member: " . htmlspecialchars($testMember['full_name']) . "</h4>\n";
        echo "<p><strong>Image Path:</strong> " . htmlspecialchars($testMember['image_path'] ?? 'No image') . "</p>\n";
        
        if (!empty($testMember['image_path'])) {
            $birthdayMemberImagePath = '../' . ltrim($testMember['image_path'], '/');
            echo "<p><strong>Expected Preview Path:</strong> " . htmlspecialchars($birthdayMemberImagePath) . "</p>\n";
            
            // Test if the file exists
            $fullPath = __DIR__ . '/' . $birthdayMemberImagePath;
            if (file_exists($fullPath)) {
                echo "<p style='color: green;'>✅ Image file exists at: " . htmlspecialchars($fullPath) . "</p>\n";
            } else {
                echo "<p style='color: red;'>❌ Image file NOT found at: " . htmlspecialchars($fullPath) . "</p>\n";
            }
        }
        echo "</div>\n";
    }
    
    echo "<div style='background-color: #cce5ff; padding: 15px; border: 1px solid #99ccff; border-radius: 4px; margin: 20px 0;'>\n";
    echo "<h3>🔄 Next Steps</h3>\n";
    echo "<ol>\n";
    echo "<li><strong>Clear browser cache</strong> (Ctrl+Shift+R)</li>\n";
    echo "<li><strong>Go back to the preview</strong>: <a href='admin/preview_template.php?id=46&embed=1' target='_blank'>Preview Template 46</a></li>\n";
    echo "<li><strong>Check if the image now displays correctly</strong></li>\n";
    echo "</ol>\n";
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<p>❌ <strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>\n";
}
?>
