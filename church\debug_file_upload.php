<?php
session_start();
require_once 'config.php';

echo "<h2>File Upload Debug</h2>";

// Auto-login as admin for testing
if (!isset($_SESSION['admin_id'])) {
    $stmt = $pdo->prepare("SELECT id, username, password FROM admins WHERE username = ?");
    $stmt->execute(['admin']);
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($admin && password_verify('admin123', $admin['password'])) {
        $_SESSION['admin_id'] = $admin['id'];
        $_SESSION['admin_username'] = $admin['username'];
        echo "<p>✅ Logged in as admin (ID: {$admin['id']})</p>";
    }
}

// 1. Check event_files table structure
echo "<h3>1. Event Files Table Structure</h3>";
try {
    $stmt = $pdo->query("DESCRIBE event_files");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($columns as $column) {
        $nullAllowed = $column['Null'] === 'YES' ? '✅ YES' : '❌ NO (Required)';
        echo "<tr>";
        echo "<td><strong>" . $column['Field'] . "</strong></td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $nullAllowed . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . ($column['Default'] ?: 'None') . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} catch (Exception $e) {
    echo "<p>❌ Error getting event_files table structure: " . $e->getMessage() . "</p>";
}

// 2. Check upload directories
echo "<h3>2. Upload Directory Check</h3>";
$base_dir = 'uploads/events/';
$promotional_dir = $base_dir . 'promotional/';
$thumbnails_dir = $base_dir . 'thumbnails/';

$directories = [
    'Base Directory' => $base_dir,
    'Promotional Directory' => $promotional_dir,
    'Thumbnails Directory' => $thumbnails_dir
];

foreach ($directories as $name => $dir) {
    $full_path = realpath($dir);
    if (is_dir($dir)) {
        $writable = is_writable($dir) ? '✅ Writable' : '❌ Not Writable';
        echo "<p><strong>$name:</strong> ✅ Exists ($full_path) - $writable</p>";
    } else {
        echo "<p><strong>$name:</strong> ❌ Does not exist ($dir)</p>";
        // Try to create it
        if (mkdir($dir, 0755, true)) {
            echo "<p>✅ Created directory: $dir</p>";
        } else {
            echo "<p>❌ Failed to create directory: $dir</p>";
        }
    }
}

// 3. Test file upload simulation
echo "<h3>3. File Upload Test</h3>";

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['test_file'])) {
    echo "<h4>Processing File Upload...</h4>";
    
    try {
        // Create a test event first
        $pdo->beginTransaction();
        
        $stmt = $pdo->prepare("
            INSERT INTO events (title, description, event_date, location, created_by, is_active)
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            'File Upload Test Event',
            'Test event for file upload debugging',
            '2025-07-20 10:00:00',
            'Test Location',
            $_SESSION['admin_id'],
            1
        ]);
        
        $event_id = $pdo->lastInsertId();
        echo "<p>✅ Test event created with ID: $event_id</p>";
        
        // Process the uploaded file
        if ($_FILES['test_file']['error'] === UPLOAD_ERR_OK) {
            $file_name = $_FILES['test_file']['name'];
            $file_tmp = $_FILES['test_file']['tmp_name'];
            $file_size = $_FILES['test_file']['size'];
            $file_type = $_FILES['test_file']['type'];
            
            echo "<p><strong>File Details:</strong></p>";
            echo "<ul>";
            echo "<li>Name: $file_name</li>";
            echo "<li>Size: " . number_format($file_size) . " bytes</li>";
            echo "<li>Type: $file_type</li>";
            echo "<li>Temp Path: $file_tmp</li>";
            echo "</ul>";
            
            // Generate safe filename
            $file_extension = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));
            $safe_filename = preg_replace('/[^a-zA-Z0-9._-]/', '_', pathinfo($file_name, PATHINFO_FILENAME));
            $unique_name = 'event_' . $event_id . '_' . time() . '_' . $safe_filename . '.' . $file_extension;
            
            // Determine target directory
            $file_category = in_array($file_extension, ['jpg', 'jpeg', 'png', 'gif']) ? 'promotional' : 'document';
            $target_dir = ($file_category === 'promotional') ? $promotional_dir : $base_dir;
            $file_path = $target_dir . $unique_name;
            
            echo "<p><strong>Target:</strong> $file_path</p>";
            
            // Move uploaded file
            if (move_uploaded_file($file_tmp, $file_path)) {
                echo "<p>✅ File moved successfully</p>";
                
                // Save to database
                $file_stmt = $pdo->prepare("
                    INSERT INTO event_files (event_id, file_name, file_path, file_type, file_size,
                                           uploaded_by, file_category, is_header_banner, display_order)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ");
                
                $file_stmt->execute([
                    $event_id,
                    $file_name,
                    $file_path,
                    $file_type,
                    $file_size,
                    $_SESSION['admin_id'],
                    $file_category,
                    0, // is_header_banner
                    1  // display_order
                ]);
                
                echo "<p>✅ File record saved to database</p>";
                
                // Clean up test data
                unlink($file_path);
                echo "<p>✅ Test file cleaned up</p>";
                
            } else {
                echo "<p>❌ Failed to move uploaded file</p>";
            }
        } else {
            echo "<p>❌ File upload error: " . $_FILES['test_file']['error'] . "</p>";
        }
        
        // Clean up test event
        $pdo->prepare("DELETE FROM events WHERE id = ?")->execute([$event_id]);
        echo "<p>✅ Test event cleaned up</p>";
        
        $pdo->commit();
        
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollback();
        }
        echo "<p>❌ Exception: " . $e->getMessage() . "</p>";
        echo "<p>File: " . $e->getFile() . " Line: " . $e->getLine() . "</p>";
    }
} else {
    ?>
    <form method="POST" enctype="multipart/form-data">
        <p>Upload a test file to debug the upload process:</p>
        <p><input type="file" name="test_file" accept="image/*,.pdf" required></p>
        <p><button type="submit">Test File Upload</button></p>
    </form>
    <?php
}

// 4. Check PHP upload settings
echo "<h3>4. PHP Upload Settings</h3>";
$upload_settings = [
    'file_uploads' => ini_get('file_uploads') ? 'Enabled' : 'Disabled',
    'upload_max_filesize' => ini_get('upload_max_filesize'),
    'post_max_size' => ini_get('post_max_size'),
    'max_file_uploads' => ini_get('max_file_uploads'),
    'memory_limit' => ini_get('memory_limit'),
    'max_execution_time' => ini_get('max_execution_time')
];

echo "<table border='1'>";
echo "<tr><th>Setting</th><th>Value</th></tr>";
foreach ($upload_settings as $setting => $value) {
    echo "<tr><td>$setting</td><td>$value</td></tr>";
}
echo "</table>";
?>
