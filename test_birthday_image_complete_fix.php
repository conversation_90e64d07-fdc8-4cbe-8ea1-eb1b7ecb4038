<?php
/**
 * Complete test to verify birthday image fix is working
 */

require_once 'config.php';

echo "<h2>Complete Birthday Image Fix Verification</h2>\n";

try {
    // Test 1: Check database templates
    echo "<h3>Test 1: Database Template Content</h3>\n";
    
    $stmt = $pdo->prepare("
        SELECT id, template_name, content
        FROM email_templates 
        WHERE is_birthday_template = 1
        ORDER BY template_name
    ");
    $stmt->execute();
    $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $templatesOK = 0;
    $templatesWithIssues = 0;
    
    foreach ($templates as $template) {
        echo "<h4>" . htmlspecialchars($template['template_name']) . "</h4>\n";
        
        // Check for hardcoded image URLs
        preg_match_all('/<img[^>]+src=["\']([^"\']+)["\'][^>]*>/i', $template['content'], $imgMatches);
        
        $hasIssues = false;
        
        if (!empty($imgMatches[1])) {
            echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>\n";
            echo "<tr><th>Image Source</th><th>Status</th></tr>\n";
            
            foreach ($imgMatches[1] as $srcUrl) {
                echo "<tr>\n";
                echo "<td><code>" . htmlspecialchars($srcUrl) . "</code></td>\n";
                
                if (strpos($srcUrl, '{') !== false && strpos($srcUrl, '}') !== false) {
                    echo "<td style='color: green;'>✅ Dynamic Placeholder</td>\n";
                } elseif (strpos($srcUrl, 'localhost') !== false || preg_match('/uploads\/\d+/', $srcUrl)) {
                    echo "<td style='color: red;'>❌ Hardcoded URL</td>\n";
                    $hasIssues = true;
                } else {
                    echo "<td style='color: orange;'>⚠️ Other</td>\n";
                }
                
                echo "</tr>\n";
            }
            echo "</table>\n";
        } else {
            echo "<p>ℹ️ No images found in template.</p>\n";
        }
        
        if ($hasIssues) {
            $templatesWithIssues++;
        } else {
            $templatesOK++;
        }
        
        echo "<hr>\n";
    }
    
    echo "<p><strong>Summary:</strong> $templatesOK templates OK, $templatesWithIssues templates with issues</p>\n";
    
    // Test 2: Template Processing
    echo "<h3>Test 2: Template Processing with Real Data</h3>\n";
    
    // Get a member with an image for testing
    $stmt = $pdo->prepare("SELECT * FROM members WHERE image_path IS NOT NULL AND image_path != '' LIMIT 1");
    $stmt->execute();
    $testMember = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$testMember) {
        echo "<p>⚠️ No member with image found. Using default data.</p>\n";
        $testMember = [
            'id' => 999,
            'full_name' => 'Test Member',
            'first_name' => 'Test',
            'image_path' => 'assets/img/default-avatar.png'
        ];
    }
    
    echo "<p><strong>Test Member:</strong> " . htmlspecialchars($testMember['full_name']) . "</p>\n";
    echo "<p><strong>Image Path:</strong> " . htmlspecialchars($testMember['image_path']) . "</p>\n";
    
    // Create test data
    $siteUrl = defined('SITE_URL') ? SITE_URL : 
        ((isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://" . $_SERVER['HTTP_HOST']);
    
    $memberImageUrl = $siteUrl . '/' . ltrim($testMember['image_path'], '/');
    
    $testMemberData = [
        'first_name' => 'John',
        'full_name' => 'John Doe',
        'birthday_member_first_name' => $testMember['first_name'],
        'birthday_member_full_name' => $testMember['full_name'],
        'birthday_member_age' => 40,
        'birthday_member_image' => $memberImageUrl,
        'birthday_member_photo_url' => $memberImageUrl,
        'member_image' => $memberImageUrl,
        'member_image_url' => $memberImageUrl,
        'image_path' => $memberImageUrl,
        'profile_photo' => $memberImageUrl,
        '_original_image_path' => $testMember['image_path'],
        '_is_birthday_notification' => true
    ];
    
    // Test the first template
    if (!empty($templates)) {
        $testTemplate = $templates[0];
        echo "<h4>Testing: " . htmlspecialchars($testTemplate['template_name']) . "</h4>\n";
        
        $processedContent = replaceTemplatePlaceholders($testTemplate['content'], $testMemberData);
        
        echo "<h5>Processed Content:</h5>\n";
        echo "<div style='border: 2px solid #007bff; padding: 15px; background-color: #f8f9fa; max-height: 300px; overflow-y: auto;'>\n";
        echo $processedContent;
        echo "</div>\n";
        
        // Check processed images
        preg_match_all('/<img[^>]+src=["\']([^"\']+)["\'][^>]*>/i', $processedContent, $processedImgMatches);
        
        if (!empty($processedImgMatches[1])) {
            echo "<h5>Images in Processed Content:</h5>\n";
            echo "<ul>\n";
            foreach ($processedImgMatches[1] as $srcUrl) {
                echo "<li><strong>" . htmlspecialchars($srcUrl) . "</strong>\n";
                
                if (strpos($srcUrl, $testMember['image_path']) !== false || 
                    strpos($srcUrl, basename($testMember['image_path'])) !== false) {
                    echo " <span style='color: green;'>✅ CORRECT MEMBER IMAGE</span>\n";
                } elseif (strpos($srcUrl, '{') !== false) {
                    echo " <span style='color: red;'>❌ UNPROCESSED PLACEHOLDER</span>\n";
                } else {
                    echo " <span style='color: orange;'>⚠️ OTHER IMAGE</span>\n";
                }
                echo "</li>\n";
            }
            echo "</ul>\n";
        }
    }
    
    // Test 3: Email Sending Detection
    echo "<h3>Test 3: Email Sending Detection Logic</h3>\n";
    
    // Test the birthday notification detection logic
    $isBirthdayNotification = false;
    $detectionMethod = 'None';
    
    // Method 1: Check explicit flag
    if (isset($testMemberData['_is_birthday_notification']) && $testMemberData['_is_birthday_notification']) {
        $isBirthdayNotification = true;
        $detectionMethod = "Explicit flag (_is_birthday_notification)";
    }
    // Method 2: Check for birthday member data
    else if (isset($testMemberData['birthday_member_name']) || 
             isset($testMemberData['birthday_member_full_name']) ||
             isset($testMemberData['birthday_member_age']) ||
             isset($testMemberData['birthday_member_photo_url'])) {
        $isBirthdayNotification = true;
        $detectionMethod = "Birthday member data presence";
    }
    
    echo "<table border='1' cellpadding='10' cellspacing='0' style='border-collapse: collapse;'>\n";
    echo "<tr><th>Test</th><th>Result</th><th>Status</th></tr>\n";
    echo "<tr><td><strong>Birthday Notification Detection</strong></td><td>" . ($isBirthdayNotification ? 'DETECTED' : 'NOT DETECTED') . "</td><td>" . ($isBirthdayNotification ? '✅' : '❌') . "</td></tr>\n";
    echo "<tr><td><strong>Detection Method</strong></td><td>$detectionMethod</td><td>ℹ️</td></tr>\n";
    echo "<tr><td><strong>Original Image Path Available</strong></td><td>" . (isset($testMemberData['_original_image_path']) ? 'YES' : 'NO') . "</td><td>" . (isset($testMemberData['_original_image_path']) ? '✅' : '❌') . "</td></tr>\n";
    echo "<tr><td><strong>Birthday Member Photo URL</strong></td><td>" . (isset($testMemberData['birthday_member_photo_url']) ? 'YES' : 'NO') . "</td><td>" . (isset($testMemberData['birthday_member_photo_url']) ? '✅' : '❌') . "</td></tr>\n";
    echo "</table>\n";
    
    // Test 4: Image File Existence
    echo "<h3>Test 4: Image File Existence</h3>\n";
    
    $localImagePath = __DIR__ . '/' . $testMember['image_path'];
    $localImagePath = str_replace('\\', '/', $localImagePath);
    $localImagePath = preg_replace('#/+#', '/', $localImagePath);
    
    echo "<table border='1' cellpadding='10' cellspacing='0' style='border-collapse: collapse;'>\n";
    echo "<tr><th>Path Type</th><th>Path</th><th>Status</th></tr>\n";
    echo "<tr><td><strong>Database Path</strong></td><td>" . htmlspecialchars($testMember['image_path']) . "</td><td>ℹ️</td></tr>\n";
    echo "<tr><td><strong>Full URL</strong></td><td>" . htmlspecialchars($memberImageUrl) . "</td><td>ℹ️</td></tr>\n";
    echo "<tr><td><strong>Local File Path</strong></td><td>" . htmlspecialchars($localImagePath) . "</td><td>" . (file_exists($localImagePath) ? '✅ EXISTS' : '❌ NOT FOUND') . "</td></tr>\n";
    echo "</table>\n";
    
    // Final Summary
    echo "<h3>Final Summary</h3>\n";
    
    $allTestsPassed = ($templatesWithIssues == 0) && $isBirthdayNotification && isset($testMemberData['_original_image_path']);
    
    if ($allTestsPassed) {
        echo "<div style='background-color: #d4edda; color: #155724; padding: 15px; border: 1px solid #c3e6cb; border-radius: 4px;'>\n";
        echo "<h4>🎉 ALL TESTS PASSED!</h4>\n";
        echo "<ul>\n";
        echo "<li>✅ Database templates use dynamic placeholders</li>\n";
        echo "<li>✅ Template processing works correctly</li>\n";
        echo "<li>✅ Birthday notification detection works</li>\n";
        echo "<li>✅ Original image path is available for embedding</li>\n";
        echo "</ul>\n";
        echo "<p><strong>The birthday member image fix should now work correctly!</strong></p>\n";
        echo "</div>\n";
    } else {
        echo "<div style='background-color: #f8d7da; color: #721c24; padding: 15px; border: 1px solid #f5c6cb; border-radius: 4px;'>\n";
        echo "<h4>⚠️ SOME TESTS FAILED</h4>\n";
        echo "<ul>\n";
        if ($templatesWithIssues > 0) {
            echo "<li>❌ $templatesWithIssues templates still have hardcoded URLs</li>\n";
        }
        if (!$isBirthdayNotification) {
            echo "<li>❌ Birthday notification detection failed</li>\n";
        }
        if (!isset($testMemberData['_original_image_path'])) {
            echo "<li>❌ Original image path not available</li>\n";
        }
        echo "</ul>\n";
        echo "<p><strong>Please review and fix the failing tests above.</strong></p>\n";
        echo "</div>\n";
    }
    
} catch (Exception $e) {
    echo "<p>❌ <strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>\n";
}
?>
