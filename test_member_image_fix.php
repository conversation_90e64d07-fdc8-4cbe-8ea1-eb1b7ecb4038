<?php
/**
 * Test the member image fix
 */

require_once 'config.php';

echo "<h2>Test Member Image Fix</h2>\n";

try {
    // Get template 46
    $stmt = $pdo->prepare("SELECT * FROM email_templates WHERE id = 46");
    $stmt->execute();
    $template = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$template) {
        echo "<p>❌ Template ID 46 not found!</p>\n";
        exit;
    }
    
    echo "<div style='border: 2px solid #007bff; margin: 20px 0; padding: 15px;'>\n";
    echo "<h3>Template: " . htmlspecialchars($template['template_name']) . " (ID: {$template['id']})</h3>\n";
    
    // Get a test member (Ndivhuwo)
    $stmt = $pdo->prepare("SELECT * FROM members WHERE full_name LIKE '%Ndivhuwo%' LIMIT 1");
    $stmt->execute();
    $testMember = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$testMember) {
        echo "<p>❌ Test member not found!</p>\n";
        exit;
    }
    
    echo "<h4>Test Member: " . htmlspecialchars($testMember['full_name']) . "</h4>\n";
    echo "<p><strong>Image Path:</strong> " . htmlspecialchars($testMember['image_path'] ?? 'No image') . "</p>\n";
    
    // Prepare member data for birthday notification
    $memberData = [
        'full_name' => $testMember['full_name'],
        'first_name' => explode(' ', $testMember['full_name'])[0],
        'email' => $testMember['email'] ?? '<EMAIL>',
        'image_path' => $testMember['image_path'],
        'birth_date' => $testMember['birth_date'] ?? '1990-07-16',
        
        // Birthday member data
        'birthday_member_name' => explode(' ', $testMember['full_name'])[0],
        'birthday_member_first_name' => explode(' ', $testMember['full_name'])[0],
        'birthday_member_full_name' => $testMember['full_name'],
        'birthday_member_email' => $testMember['email'] ?? '<EMAIL>',
        'birthday_member_age' => '38',
        'age' => '38',
        
        // Organization data
        'organization_name' => 'Freedom Assembly Church',
        'organization_type' => 'church',
        'recipient_full_name' => 'John Doe',
        
        // Birthday context
        'days_text' => 'tomorrow',
        'upcoming_birthday_formatted' => 'Wednesday, July 16, 2025',
        
        // Special flag for birthday notification
        '_is_birthday_notification' => true
    ];
    
    echo "<h4>Testing replaceTemplatePlaceholders Function:</h4>\n";
    
    // Test the function
    $processedContent = replaceTemplatePlaceholders($template['content'], $memberData);
    
    echo "<h5>Original Template Content (first 500 chars):</h5>\n";
    echo "<pre style='background-color: #f8f9fa; padding: 10px; border: 1px solid #dee2e6; max-height: 200px; overflow-y: auto;'>\n";
    echo htmlspecialchars(substr($template['content'], 0, 500)) . "...\n";
    echo "</pre>\n";
    
    echo "<h5>Processed Content (first 1000 chars):</h5>\n";
    echo "<pre style='background-color: #e6ffe6; padding: 10px; border: 1px solid #99ff99; max-height: 300px; overflow-y: auto;'>\n";
    echo htmlspecialchars(substr($processedContent, 0, 1000)) . "...\n";
    echo "</pre>\n";
    
    // Check if {member_image} was replaced with HTML
    if (strpos($processedContent, '<img') !== false) {
        echo "<p style='color: green;'>✅ <strong>{member_image} was replaced with HTML img tag!</strong></p>\n";
    } else {
        echo "<p style='color: red;'>❌ <strong>{member_image} was NOT replaced with HTML img tag!</strong></p>\n";
    }
    
    // Check if any placeholders remain
    if (strpos($processedContent, '{member_image}') !== false) {
        echo "<p style='color: red;'>❌ <strong>{member_image} placeholder still exists!</strong></p>\n";
    } else {
        echo "<p style='color: green;'>✅ <strong>{member_image} placeholder was replaced!</strong></p>\n";
    }
    
    // Check for URLs instead of HTML
    if (preg_match('/http:\/\/[^<>\s]+\.(png|jpg|jpeg|gif)/', $processedContent)) {
        echo "<p style='color: orange;'>⚠️ <strong>Found raw image URLs in content (might be correct for email)</strong></p>\n";
    }
    
    echo "<h5>Visual Preview:</h5>\n";
    echo "<div style='border: 2px solid #28a745; padding: 15px; background-color: #f8f9fa; max-height: 400px; overflow-y: auto;'>\n";
    echo $processedContent;
    echo "</div>\n";
    
    echo "</div>\n";
    
    // Test the preview system directly
    echo "<div style='background-color: #e3f2fd; padding: 15px; border: 1px solid #2196f3; border-radius: 4px; margin: 20px 0;'>\n";
    echo "<h3>🔗 Test Preview Links</h3>\n";
    echo "<p><a href='admin/preview_template.php?id=46' target='_blank'>Preview Template 46</a></p>\n";
    echo "<p><a href='admin/preview_template.php?id=46&embed=1' target='_blank'>Embedded Preview Template 46</a></p>\n";
    echo "</div>\n";
    
    // Instructions
    echo "<div style='background-color: #d4edda; color: #155724; padding: 15px; border: 1px solid #c3e6cb; border-radius: 4px; margin: 20px 0;'>\n";
    echo "<h3>🎉 Next Steps</h3>\n";
    echo "<ol>\n";
    echo "<li><strong>Clear browser cache</strong> (Ctrl+Shift+R)</li>\n";
    echo "<li><strong>Click the preview links above</strong></li>\n";
    echo "<li><strong>Check if the member image now displays as an actual image instead of a URL</strong></li>\n";
    echo "</ol>\n";
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<p>❌ <strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>\n";
}
?>
