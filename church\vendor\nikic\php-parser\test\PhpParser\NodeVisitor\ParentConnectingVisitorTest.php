<?php declare(strict_types=1);

namespace Php<PERSON><PERSON><PERSON>\NodeVisitor;

use Php<PERSON><PERSON>er\Node\Stmt\ClassMethod;
use Php<PERSON>arser\NodeFinder;
use PhpParser\NodeTraverser;
use Php<PERSON>arser\ParserFactory;

final class ParentConnectingVisitorTest extends \PHPUnit\Framework\TestCase {
    public function testConnectsChildNodeToParentNode(): void {
        $ast = (new ParserFactory())->createForNewestSupportedVersion()->parse(
            '<?php class C { public function m() {} }'
        );

        $traverser = new NodeTraverser();

        $traverser->addVisitor(new ParentConnectingVisitor());

        $ast = $traverser->traverse($ast);

        $node = (new NodeFinder())->findFirstInstanceof($ast, ClassMethod::class);

        $this->assertSame('C', $node->getAttribute('parent')->name->toString());
    }
}
