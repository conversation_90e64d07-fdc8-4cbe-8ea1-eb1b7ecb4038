<?php
require_once 'config.php';

echo "<h2>Database Tables Check</h2>";

try {
    // Show all tables
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<h3>All Tables in Database:</h3>";
    echo "<ul>";
    foreach ($tables as $table) {
        echo "<li>$table</li>";
    }
    echo "</ul>";
    
    // Look for admin-related tables
    $admin_tables = array_filter($tables, function($table) {
        return strpos(strtolower($table), 'admin') !== false || strpos(strtolower($table), 'user') !== false;
    });
    
    echo "<h3>Admin/User Related Tables:</h3>";
    if (empty($admin_tables)) {
        echo "<p>No admin/user tables found</p>";
    } else {
        foreach ($admin_tables as $table) {
            echo "<h4>Table: $table</h4>";
            $stmt = $pdo->query("DESCRIBE $table");
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            echo "<table border='1'>";
            echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
            foreach ($columns as $column) {
                echo "<tr>";
                echo "<td>" . $column['Field'] . "</td>";
                echo "<td>" . $column['Type'] . "</td>";
                echo "<td>" . $column['Null'] . "</td>";
                echo "<td>" . $column['Key'] . "</td>";
                echo "<td>" . $column['Default'] . "</td>";
                echo "<td>" . $column['Extra'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            // Show sample data
            $stmt = $pdo->query("SELECT * FROM $table LIMIT 5");
            $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
            if (!empty($data)) {
                echo "<h5>Sample Data:</h5>";
                echo "<pre>" . print_r($data, true) . "</pre>";
            }
        }
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}
?>
