<?php
/**
 * Fix all birthday templates with hardcoded image URLs
 */

require_once 'config.php';

echo "<h2>Fix All Birthday Templates</h2>\n";

try {
    // Get all birthday templates
    $stmt = $pdo->prepare("
        SELECT id, template_name, content
        FROM email_templates 
        WHERE is_birthday_template = 1
        ORDER BY template_name
    ");
    $stmt->execute();
    $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($templates)) {
        echo "<p>❌ No birthday templates found!</p>\n";
        exit;
    }
    
    echo "<p>Found " . count($templates) . " birthday template(s) to check.</p>\n";
    
    $fixedCount = 0;
    
    foreach ($templates as $template) {
        echo "<div style='border: 2px solid #007bff; margin: 20px 0; padding: 15px;'>\n";
        echo "<h3>Template: " . htmlspecialchars($template['template_name']) . " (ID: {$template['id']})</h3>\n";
        
        $originalContent = $template['content'];
        $fixedContent = $originalContent;
        
        // Check for hardcoded image URLs
        preg_match_all('/<img[^>]+src=["\']([^"\']+)["\'][^>]*>/i', $originalContent, $imgMatches);
        
        $hasHardcodedImages = false;
        
        if (!empty($imgMatches[1])) {
            echo "<h4>Images Found:</h4>\n";
            echo "<ul>\n";
            foreach ($imgMatches[1] as $srcUrl) {
                echo "<li><strong>" . htmlspecialchars($srcUrl) . "</strong>\n";
                
                if (strpos($srcUrl, 'localhost') !== false || 
                    strpos($srcUrl, 'uploads/') !== false ||
                    preg_match('/uploads\/[a-zA-Z0-9]+/', $srcUrl)) {
                    echo " <span style='color: red;'>❌ HARDCODED - WILL FIX</span>\n";
                    $hasHardcodedImages = true;
                } elseif (strpos($srcUrl, '{') !== false && strpos($srcUrl, '}') !== false) {
                    echo " <span style='color: green;'>✅ PLACEHOLDER - OK</span>\n";
                } else {
                    echo " <span style='color: orange;'>⚠️ OTHER</span>\n";
                }
                echo "</li>\n";
            }
            echo "</ul>\n";
        } else {
            echo "<p>ℹ️ No images found in this template.</p>\n";
        }
        
        if ($hasHardcodedImages) {
            echo "<h4>🔧 Fixing Template...</h4>\n";
            
            // Fix 1: Replace img tags with hardcoded localhost URLs
            $fixedContent = preg_replace(
                '/<img([^>]+)src=["\'][^"\']*localhost[^"\']*uploads[^"\']*["\']([^>]*)>/i',
                '<img$1src="{birthday_member_image}"$2>',
                $fixedContent
            );
            
            // Fix 2: Replace img tags with hardcoded upload paths
            $fixedContent = preg_replace(
                '/<img([^>]+)src=["\'][^"\']*uploads\/[^"\']*["\']([^>]*)>/i',
                '<img$1src="{birthday_member_image}"$2>',
                $fixedContent
            );
            
            // Fix 3: Replace direct URL references in content
            $fixedContent = preg_replace(
                '/http:\/\/localhost\/campaign\/church\/uploads\/[a-zA-Z0-9]+\.(png|jpg|jpeg|gif)/i',
                '{birthday_member_image}',
                $fixedContent
            );
            
            // Fix 4: Replace any remaining upload references
            $fixedContent = preg_replace(
                '/uploads\/[a-zA-Z0-9]+\.(png|jpg|jpeg|gif)/i',
                '{birthday_member_image}',
                $fixedContent
            );
            
            if ($fixedContent !== $originalContent) {
                echo "<h5>Before:</h5>\n";
                echo "<pre style='background-color: #ffe6e6; padding: 10px; border: 1px solid #ff9999; max-height: 200px; overflow-y: auto;'>\n";
                echo htmlspecialchars($originalContent);
                echo "</pre>\n";
                
                echo "<h5>After:</h5>\n";
                echo "<pre style='background-color: #e6ffe6; padding: 10px; border: 1px solid #99ff99; max-height: 200px; overflow-y: auto;'>\n";
                echo htmlspecialchars($fixedContent);
                echo "</pre>\n";
                
                // Update the database
                $updateStmt = $pdo->prepare("UPDATE email_templates SET content = ? WHERE id = ?");
                $result = $updateStmt->execute([$fixedContent, $template['id']]);
                
                if ($result) {
                    echo "<p style='color: green;'>✅ <strong>Template updated successfully!</strong></p>\n";
                    $fixedCount++;
                } else {
                    echo "<p style='color: red;'>❌ <strong>Failed to update template!</strong></p>\n";
                }
            } else {
                echo "<p>ℹ️ No changes made to template content.</p>\n";
            }
        } else {
            echo "<p style='color: green;'>✅ <strong>Template is already correct!</strong></p>\n";
        }
        
        echo "</div>\n";
    }
    
    // Summary
    echo "<div style='background-color: #d4edda; color: #155724; padding: 15px; border: 1px solid #c3e6cb; border-radius: 4px; margin: 20px 0;'>\n";
    echo "<h3>🎉 Fix Complete!</h3>\n";
    echo "<ul>\n";
    echo "<li><strong>Templates Processed:</strong> " . count($templates) . "</li>\n";
    echo "<li><strong>Templates Fixed:</strong> $fixedCount</li>\n";
    echo "<li><strong>Templates Already OK:</strong> " . (count($templates) - $fixedCount) . "</li>\n";
    echo "</ul>\n";
    echo "</div>\n";
    
    // Test the first template
    if (!empty($templates)) {
        echo "<h3>Testing First Template</h3>\n";
        
        // Get updated template content
        $stmt = $pdo->prepare("SELECT content FROM email_templates WHERE id = ?");
        $stmt->execute([$templates[0]['id']]);
        $updatedTemplate = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Get a test member
        $stmt = $pdo->prepare("SELECT * FROM members WHERE image_path IS NOT NULL AND image_path != '' LIMIT 1");
        $stmt->execute();
        $testMember = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($testMember && $updatedTemplate) {
            $siteUrl = defined('SITE_URL') ? SITE_URL : 
                ((isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://" . $_SERVER['HTTP_HOST']);
            
            $memberImageUrl = $siteUrl . '/' . ltrim($testMember['image_path'], '/');
            
            $testData = [
                'birthday_member_first_name' => $testMember['first_name'],
                'birthday_member_full_name' => $testMember['full_name'],
                'birthday_member_image' => $memberImageUrl,
                'birthday_member_photo_url' => $memberImageUrl,
                'member_image' => $memberImageUrl,
                'birthday_member_age' => 40,
                'first_name' => 'Test Recipient'
            ];
            
            $processedContent = replaceTemplatePlaceholders($updatedTemplate['content'], $testData);
            
            echo "<h4>Test Preview:</h4>\n";
            echo "<div style='border: 2px solid #28a745; padding: 15px; background-color: #f8f9fa;'>\n";
            echo $processedContent;
            echo "</div>\n";
        }
    }
    
    echo "<div style='background-color: #cce5ff; padding: 15px; border: 1px solid #99ccff; border-radius: 4px; margin: 20px 0;'>\n";
    echo "<h3>🔄 Next Steps</h3>\n";
    echo "<ol>\n";
    echo "<li><strong>Refresh the birthday notification page</strong> (F5 or Ctrl+R)</li>\n";
    echo "<li><strong>Select the template again</strong> from the dropdown</li>\n";
    echo "<li><strong>Check the preview</strong> - it should now show the correct member image</li>\n";
    echo "<li><strong>Send a test notification</strong> to verify the fix works</li>\n";
    echo "</ol>\n";
    echo "<p><strong>The templates have been updated in the database. The preview should now display the correct member images!</strong></p>\n";
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<p>❌ <strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>\n";
}
?>
