<?php
session_start();
require_once 'config.php';

echo "<h2>Detailed Event Creation Debug</h2>";

// 1. Check events table structure
echo "<h3>1. Events Table Structure</h3>";
try {
    $stmt = $pdo->query("DESCRIBE events");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($columns as $column) {
        $nullAllowed = $column['Null'] === 'YES' ? '✅ YES' : '❌ NO (Required)';
        echo "<tr>";
        echo "<td><strong>" . $column['Field'] . "</strong></td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $nullAllowed . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . ($column['Default'] ?: 'None') . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} catch (Exception $e) {
    echo "<p>❌ Error getting table structure: " . $e->getMessage() . "</p>";
}

// 2. Check admin login status
echo "<h3>2. Admin Login Status</h3>";
if (!isset($_SESSION['admin_id'])) {
    echo "<p>❌ Admin not logged in. <a href='admin/login.php'>Login first</a></p>";
    exit;
}
echo "<p>✅ Admin logged in: ID=" . $_SESSION['admin_id'] . "</p>";

// 3. Test with minimal required data
echo "<h3>3. Test Event Creation with Minimal Data</h3>";

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<h4>POST Data Received:</h4>";
    echo "<pre>" . print_r($_POST, true) . "</pre>";
    
    // Check for missing required fields
    $required_fields = ['title', 'start_datetime', 'end_datetime'];
    $missing_fields = [];
    foreach ($required_fields as $field) {
        if (empty($_POST[$field])) {
            $missing_fields[] = $field;
        }
    }
    
    if (!empty($missing_fields)) {
        echo "<p>❌ Missing required fields: " . implode(', ', $missing_fields) . "</p>";
    } else {
        echo "<p>✅ All required fields present</p>";
        
        // Test database insertion step by step
        echo "<h4>Database Insertion Test:</h4>";
        
        try {
            $pdo->beginTransaction();
            echo "<p>✅ Transaction started</p>";
            
            // Prepare the exact same query as in create_event_with_materials.php
            $stmt = $pdo->prepare("
                INSERT INTO events (title, description, event_date, location,
                                  max_attendees, category_id, created_by, is_active)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            echo "<p>✅ Query prepared</p>";
            
            // Prepare the data exactly as in the original script
            $data = [
                $_POST['title'],
                !empty($_POST['description']) ? $_POST['description'] : null,
                $_POST['start_datetime'],
                !empty($_POST['location']) ? $_POST['location'] : null,
                !empty($_POST['capacity']) ? (int)$_POST['capacity'] : null,
                !empty($_POST['category_id']) ? (int)$_POST['category_id'] : null,
                $_SESSION['admin_id'],
                ($_POST['status'] === 'published') ? 1 : 0
            ];
            
            echo "<p><strong>Data being inserted:</strong></p>";
            echo "<pre>";
            echo "title: " . var_export($data[0], true) . "\n";
            echo "description: " . var_export($data[1], true) . "\n";
            echo "event_date: " . var_export($data[2], true) . "\n";
            echo "location: " . var_export($data[3], true) . "\n";
            echo "max_attendees: " . var_export($data[4], true) . "\n";
            echo "category_id: " . var_export($data[5], true) . "\n";
            echo "created_by: " . var_export($data[6], true) . "\n";
            echo "is_active: " . var_export($data[7], true) . "\n";
            echo "</pre>";
            
            // Execute the query
            $result = $stmt->execute($data);
            
            if ($result) {
                $event_id = $pdo->lastInsertId();
                echo "<p>✅ Event created successfully with ID: $event_id</p>";
                
                // Clean up test event
                $pdo->prepare("DELETE FROM events WHERE id = ?")->execute([$event_id]);
                echo "<p>✅ Test event cleaned up</p>";
                
                $pdo->commit();
                echo "<p>✅ Transaction committed</p>";
            } else {
                echo "<p>❌ Query execution failed</p>";
                echo "<p>Error info: " . print_r($stmt->errorInfo(), true) . "</p>";
                $pdo->rollback();
            }
            
        } catch (Exception $e) {
            if ($pdo->inTransaction()) {
                $pdo->rollback();
            }
            echo "<p>❌ Exception: " . $e->getMessage() . "</p>";
            echo "<p>Stack trace: " . $e->getTraceAsString() . "</p>";
        }
    }
} else {
    // Show form with all possible fields
    ?>
    <form method="POST">
        <h4>Test Event Creation Form</h4>
        <table>
            <tr><td>Title (Required):</td><td><input type="text" name="title" value="Debug Test Event" required></td></tr>
            <tr><td>Description:</td><td><textarea name="description">Debug test description</textarea></td></tr>
            <tr><td>Start Date/Time (Required):</td><td><input type="datetime-local" name="start_datetime" value="2025-07-20T10:00" required></td></tr>
            <tr><td>End Date/Time (Required):</td><td><input type="datetime-local" name="end_datetime" value="2025-07-20T12:00" required></td></tr>
            <tr><td>Location:</td><td><input type="text" name="location" value="Debug Location"></td></tr>
            <tr><td>Capacity:</td><td><input type="number" name="capacity" value="50"></td></tr>
            <tr><td>Category ID:</td><td><input type="number" name="category_id" value=""></td></tr>
            <tr><td>Status:</td><td>
                <select name="status">
                    <option value="published">Published</option>
                    <option value="draft">Draft</option>
                </select>
            </td></tr>
        </table>
        <p><button type="submit">Test Event Creation</button></p>
    </form>
    <?php
}
?>
