<?php
// Create a very small test image (under 1KB)
$width = 50;
$height = 30;

$image = imagecreate($width, $height);
$white = imagecolorallocate($image, 255, 255, 255);
$blue = imagecolorallocate($image, 0, 100, 200);

imagefill($image, 0, 0, $white);
imagestring($image, 2, 5, 8, 'TEST', $blue);

$filename = 'small_test.jpg';
imagejpeg($image, $filename, 85);
imagedestroy($image);

$size = filesize($filename);
echo "Created test image: $filename ($size bytes)";
?>
