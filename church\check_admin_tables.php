<?php
require_once 'config.php';

echo "<h2>Checking Admin Authentication Tables</h2>";

// Get all tables in the database
try {
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<h3>All Tables in Database:</h3>";
    echo "<ul>";
    foreach ($tables as $table) {
        echo "<li>$table</li>";
    }
    echo "</ul>";
    
    // Look for admin-related tables
    $adminTables = array_filter($tables, function($table) {
        return stripos($table, 'admin') !== false || stripos($table, 'user') !== false;
    });
    
    echo "<h3>Admin/User Related Tables:</h3>";
    if (empty($adminTables)) {
        echo "<p>No admin/user tables found</p>";
    } else {
        foreach ($adminTables as $table) {
            echo "<h4>Table: $table</h4>";
            try {
                $stmt = $pdo->query("DESCRIBE $table");
                $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                echo "<table border='1'>";
                echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
                foreach ($columns as $column) {
                    echo "<tr>";
                    echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
                    echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
                    echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
                    echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
                    echo "<td>" . htmlspecialchars($column['Default'] ?? '') . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
                
                // Show sample data
                $stmt = $pdo->query("SELECT * FROM $table LIMIT 5");
                $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
                if (!empty($data)) {
                    echo "<h5>Sample Data:</h5>";
                    echo "<table border='1'>";
                    echo "<tr>";
                    foreach (array_keys($data[0]) as $key) {
                        echo "<th>" . htmlspecialchars($key) . "</th>";
                    }
                    echo "</tr>";
                    foreach ($data as $row) {
                        echo "<tr>";
                        foreach ($row as $value) {
                            echo "<td>" . htmlspecialchars($value ?? '') . "</td>";
                        }
                        echo "</tr>";
                    }
                    echo "</table>";
                }
                
            } catch (Exception $e) {
                echo "<p>Error describing table $table: " . $e->getMessage() . "</p>";
            }
        }
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}
?>
