<?php
require_once 'config.php';

try {
    // Check if events table exists and get its structure
    $stmt = $pdo->query("DESCRIBE events");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>Events Table Structure:</h2>";
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Default']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Extra']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check if event_files table exists
    try {
        $stmt = $pdo->query("DESCRIBE event_files");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h2>Event Files Table Structure:</h2>";
        echo "<table border='1'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Default']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Extra']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } catch (Exception $e) {
        echo "<h2>Event Files Table: " . $e->getMessage() . "</h2>";
    }
    
    // Test a simple insert to see what happens
    echo "<h2>Testing Event Creation:</h2>";
    
    try {
        $stmt = $pdo->prepare("
            INSERT INTO events (title, description, event_date, location,
                              max_attendees, category_id, created_by, is_active)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $result = $stmt->execute([
            'Test Event',
            'Test Description',
            '2025-07-20 10:00:00',
            'Test Location',
            50,
            null,
            1, // Assuming admin_id = 1
            1
        ]);
        
        if ($result) {
            $event_id = $pdo->lastInsertId();
            echo "SUCCESS: Event created with ID: $event_id<br>";
            
            // Clean up test event
            $pdo->prepare("DELETE FROM events WHERE id = ?")->execute([$event_id]);
            echo "Test event cleaned up.<br>";
        } else {
            echo "FAILED: Could not create test event<br>";
        }
        
    } catch (Exception $e) {
        echo "ERROR: " . $e->getMessage() . "<br>";
    }
    
} catch (Exception $e) {
    echo "Database Error: " . $e->getMessage();
}
?>
