<?php
/**
 * Test both fixes: Birthday template image and notification counter
 */

require_once 'config.php';

echo "<h2>Testing Both Fixes</h2>\n";

echo "<div style='background-color: #e3f2fd; padding: 15px; border: 1px solid #2196f3; border-radius: 4px; margin: 20px 0;'>\n";
echo "<h3>🔧 Fix Summary</h3>\n";
echo "<ol>\n";
echo "<li><strong>Birthday Template Image Fix:</strong> Replaced hardcoded image URLs with dynamic placeholders</li>\n";
echo "<li><strong>Notification Counter Fix:</strong> Improved visibility with better positioning and styling</li>\n";
echo "</ol>\n";
echo "</div>\n";

// Test 1: Check template content
echo "<h3>1. Birthday Template Content Check</h3>\n";

try {
    $stmt = $pdo->prepare("
        SELECT id, template_name, content
        FROM email_templates 
        WHERE template_name LIKE '%Birthday Notification%' OR template_name LIKE '%Member Notification%'
        ORDER BY template_name
    ");
    $stmt->execute();
    $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($templates as $template) {
        echo "<div style='border: 1px solid #28a745; margin: 10px 0; padding: 10px; background-color: #f8fff8;'>\n";
        echo "<h4>" . htmlspecialchars($template['template_name']) . "</h4>\n";
        
        if (strpos($template['content'], '{birthday_member_image}') !== false) {
            echo "<p style='color: green;'>✅ Contains {birthday_member_image} placeholder</p>\n";
        } else {
            echo "<p style='color: red;'>❌ Missing {birthday_member_image} placeholder</p>\n";
        }
        
        if (strpos($template['content'], 'localhost') !== false || 
            preg_match('/uploads\/[a-zA-Z0-9]+\.(png|jpg|jpeg|gif)/', $template['content'])) {
            echo "<p style='color: red;'>❌ Still contains hardcoded image URLs</p>\n";
        } else {
            echo "<p style='color: green;'>✅ No hardcoded image URLs found</p>\n";
        }
        echo "</div>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error checking templates: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

// Test 2: Check notification counter styling
echo "<h3>2. Notification Counter Styling Check</h3>\n";

$sidebarFile = 'admin/includes/sidebar.php';
$headerFile = 'admin/includes/header.php';

if (file_exists($sidebarFile)) {
    $sidebarContent = file_get_contents($sidebarFile);
    if (strpos($sidebarContent, 'top: -8px; right: -8px') !== false) {
        echo "<p style='color: green;'>✅ Sidebar notification badge styling updated</p>\n";
    } else {
        echo "<p style='color: red;'>❌ Sidebar notification badge styling not updated</p>\n";
    }
} else {
    echo "<p style='color: red;'>❌ Sidebar file not found</p>\n";
}

if (file_exists($headerFile)) {
    $headerContent = file_get_contents($headerFile);
    if (strpos($headerContent, "newBadge.style.top = '-8px'") !== false) {
        echo "<p style='color: green;'>✅ Header notification badge styling updated</p>\n";
    } else {
        echo "<p style='color: red;'>❌ Header notification badge styling not updated</p>\n";
    }
} else {
    echo "<p style='color: red;'>❌ Header file not found</p>\n";
}

// Test 3: Preview test
echo "<h3>3. Template Preview Test</h3>\n";

if (!empty($templates)) {
    $testTemplate = $templates[0];
    
    // Get test member (Ndivhuwo)
    $stmt = $pdo->prepare("SELECT * FROM members WHERE full_name LIKE '%Ndivhuwo%' OR first_name LIKE '%Ndivhuwo%' LIMIT 1");
    $stmt->execute();
    $testMember = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($testMember) {
        echo "<div style='border: 2px solid #17a2b8; padding: 15px; background-color: #f0f9ff;'>\n";
        echo "<h4>Test Member: " . htmlspecialchars($testMember['full_name']) . "</h4>\n";
        echo "<p><strong>Image Path:</strong> " . htmlspecialchars($testMember['image_path'] ?? 'No image') . "</p>\n";
        
        if (!empty($testMember['image_path'])) {
            $siteUrl = defined('SITE_URL') ? SITE_URL : 
                ((isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://" . $_SERVER['HTTP_HOST']);
            
            $memberImageUrl = $siteUrl . '/campaign/church/' . ltrim($testMember['image_path'], '/');
            
            echo "<p><strong>Preview URL:</strong> <a href='admin/preview_template.php?id={$testTemplate['id']}&embed=1' target='_blank'>Click to test preview</a></p>\n";
            echo "<p><strong>Expected Image URL:</strong> " . htmlspecialchars($memberImageUrl) . "</p>\n";
        }
        echo "</div>\n";
    }
}

echo "<div style='background-color: #d4edda; color: #155724; padding: 15px; border: 1px solid #c3e6cb; border-radius: 4px; margin: 20px 0;'>\n";
echo "<h3>🎉 Testing Instructions</h3>\n";
echo "<h4>For Birthday Template Image Fix:</h4>\n";
echo "<ol>\n";
echo "<li>Go back to the admin birthday notification page</li>\n";
echo "<li>Refresh the page (F5 or Ctrl+R)</li>\n";
echo "<li>Click on Ndivhuwo Machiba's birthday notification</li>\n";
echo "<li>Select a template from the dropdown</li>\n";
echo "<li>Check the preview - it should now show the actual member image instead of a URL</li>\n";
echo "</ol>\n";

echo "<h4>For Notification Counter Fix:</h4>\n";
echo "<ol>\n";
echo "<li>Look at the sidebar notification bell icon</li>\n";
echo "<li>The red notification counter should now be more visible on top of the bell</li>\n";
echo "<li>It should have a white border and shadow for better visibility</li>\n";
echo "</ol>\n";
echo "</div>\n";

echo "<div style='background-color: #fff3cd; color: #856404; padding: 15px; border: 1px solid #ffeaa7; border-radius: 4px; margin: 20px 0;'>\n";
echo "<h3>⚠️ Important Notes</h3>\n";
echo "<ul>\n";
echo "<li>If you still see issues, try clearing your browser cache (Ctrl+Shift+R)</li>\n";
echo "<li>The notification counter fix will be visible immediately</li>\n";
echo "<li>The birthday template fix requires refreshing the birthday notification page</li>\n";
echo "</ul>\n";
echo "</div>\n";

?>
