<?php
session_start();
require_once 'config.php';

header('Content-Type: application/json');

// Debug session information
$debug_info = [
    'session_id' => session_id(),
    'session_name' => session_name(),
    'session_status' => session_status(),
    'session_data' => $_SESSION,
    'cookies' => $_COOKIE,
    'admin_logged_in' => isset($_SESSION['admin_id']),
    'admin_id' => $_SESSION['admin_id'] ?? null,
    'admin_name' => $_SESSION['admin_name'] ?? null,
    'request_method' => $_SERVER['REQUEST_METHOD'],
    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
    'remote_addr' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
];

echo json_encode($debug_info, JSON_PRETTY_PRINT);
?>
