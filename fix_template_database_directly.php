<?php
/**
 * Direct database fix for birthday notification templates
 */

require_once 'config.php';

echo "<h2>Direct Database Template Fix</h2>\n";

try {
    // Get the specific template that's being used in the screenshot
    $stmt = $pdo->prepare("
        SELECT id, template_name, content
        FROM email_templates 
        WHERE template_name = 'Member Upcoming Birthday Notification 1'
    ");
    $stmt->execute();
    $template = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$template) {
        echo "<p>❌ Template 'Member Upcoming Birthday Notification 1' not found!</p>\n";
        
        // Show all birthday templates
        $stmt = $pdo->prepare("
            SELECT id, template_name, content
            FROM email_templates 
            WHERE is_birthday_template = 1
            ORDER BY template_name
        ");
        $stmt->execute();
        $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>Available Birthday Templates:</h3>\n";
        foreach ($templates as $t) {
            echo "<p><strong>ID:</strong> {$t['id']} - <strong>Name:</strong> " . htmlspecialchars($t['template_name']) . "</p>\n";
        }
        exit;
    }
    
    echo "<h3>Found Template: " . htmlspecialchars($template['template_name']) . "</h3>\n";
    echo "<p><strong>Template ID:</strong> {$template['id']}</p>\n";
    
    echo "<h4>Current Template Content:</h4>\n";
    echo "<pre style='background-color: #ffe6e6; padding: 10px; border: 1px solid #ff9999; white-space: pre-wrap;'>\n";
    echo htmlspecialchars($template['content']);
    echo "</pre>\n";
    
    // Check for hardcoded image URLs
    preg_match_all('/<img[^>]+src=["\']([^"\']+)["\'][^>]*>/i', $template['content'], $imgMatches);
    
    if (!empty($imgMatches[1])) {
        echo "<h4>Images Found in Template:</h4>\n";
        echo "<ul>\n";
        foreach ($imgMatches[1] as $srcUrl) {
            echo "<li><strong>" . htmlspecialchars($srcUrl) . "</strong>\n";
            
            if (strpos($srcUrl, 'localhost') !== false || strpos($srcUrl, 'uploads/') !== false) {
                echo " <span style='color: red;'>❌ HARDCODED - NEEDS FIX</span>\n";
            } elseif (strpos($srcUrl, '{') !== false && strpos($srcUrl, '}') !== false) {
                echo " <span style='color: green;'>✅ PLACEHOLDER - OK</span>\n";
            }
            echo "</li>\n";
        }
        echo "</ul>\n";
    }
    
    // Fix the template content
    $originalContent = $template['content'];
    $fixedContent = $originalContent;
    
    // Replace hardcoded image URLs with placeholder
    $fixedContent = preg_replace(
        '/<img([^>]+)src=["\'][^"\']*localhost[^"\']*uploads[^"\']*["\']([^>]*)>/i',
        '<img$1src="{birthday_member_image}"$2>',
        $fixedContent
    );
    
    // Also replace any other hardcoded upload URLs
    $fixedContent = preg_replace(
        '/<img([^>]+)src=["\'][^"\']*uploads\/[^"\']*["\']([^>]*)>/i',
        '<img$1src="{birthday_member_image}"$2>',
        $fixedContent
    );
    
    // Replace any remaining hardcoded image references in the content
    $fixedContent = preg_replace(
        '/http:\/\/localhost\/campaign\/church\/uploads\/[a-zA-Z0-9]+\.png/',
        '{birthday_member_image}',
        $fixedContent
    );
    
    if ($fixedContent !== $originalContent) {
        echo "<h4>Fixed Template Content:</h4>\n";
        echo "<pre style='background-color: #e6ffe6; padding: 10px; border: 1px solid #99ff99; white-space: pre-wrap;'>\n";
        echo htmlspecialchars($fixedContent);
        echo "</pre>\n";
        
        // Update the database
        $updateStmt = $pdo->prepare("UPDATE email_templates SET content = ? WHERE id = ?");
        $result = $updateStmt->execute([$fixedContent, $template['id']]);
        
        if ($result) {
            echo "<div style='background-color: #d4edda; color: #155724; padding: 15px; border: 1px solid #c3e6cb; border-radius: 4px;'>\n";
            echo "<h4>✅ Template Updated Successfully!</h4>\n";
            echo "<p>The template has been updated in the database. The preview should now show the correct member image.</p>\n";
            echo "</div>\n";
        } else {
            echo "<div style='background-color: #f8d7da; color: #721c24; padding: 15px; border: 1px solid #f5c6cb; border-radius: 4px;'>\n";
            echo "<h4>❌ Failed to Update Template</h4>\n";
            echo "<p>There was an error updating the template in the database.</p>\n";
            echo "</div>\n";
        }
    } else {
        echo "<p>ℹ️ No changes needed - template already uses placeholders.</p>\n";
    }
    
    // Test the fixed template
    echo "<h3>Testing Fixed Template</h3>\n";
    
    // Get a member with an image for testing
    $stmt = $pdo->prepare("SELECT * FROM members WHERE image_path IS NOT NULL AND image_path != '' LIMIT 1");
    $stmt->execute();
    $testMember = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($testMember) {
        echo "<p><strong>Test Member:</strong> " . htmlspecialchars($testMember['full_name']) . "</p>\n";
        echo "<p><strong>Image Path:</strong> " . htmlspecialchars($testMember['image_path']) . "</p>\n";
        
        $siteUrl = defined('SITE_URL') ? SITE_URL : 
            ((isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://" . $_SERVER['HTTP_HOST']);
        
        $memberImageUrl = $siteUrl . '/' . ltrim($testMember['image_path'], '/');
        
        $testData = [
            'birthday_member_first_name' => $testMember['first_name'],
            'birthday_member_full_name' => $testMember['full_name'],
            'birthday_member_image' => $memberImageUrl,
            'birthday_member_photo_url' => $memberImageUrl,
            'member_image' => $memberImageUrl,
            'birthday_member_age' => 40,
            'first_name' => 'Test Recipient'
        ];
        
        $processedContent = replaceTemplatePlaceholders($fixedContent, $testData);
        
        echo "<h4>Processed Template Preview:</h4>\n";
        echo "<div style='border: 2px solid #007bff; padding: 15px; background-color: #f8f9fa;'>\n";
        echo $processedContent;
        echo "</div>\n";
        
        // Check if the image URL is correct in processed content
        preg_match_all('/<img[^>]+src=["\']([^"\']+)["\'][^>]*>/i', $processedContent, $processedImgMatches);
        
        if (!empty($processedImgMatches[1])) {
            echo "<h5>Images in Processed Content:</h5>\n";
            echo "<ul>\n";
            foreach ($processedImgMatches[1] as $srcUrl) {
                echo "<li><strong>" . htmlspecialchars($srcUrl) . "</strong>\n";
                
                if (strpos($srcUrl, $testMember['image_path']) !== false || 
                    strpos($srcUrl, basename($testMember['image_path'])) !== false) {
                    echo " <span style='color: green;'>✅ CORRECT MEMBER IMAGE</span>\n";
                } elseif (strpos($srcUrl, '{') !== false) {
                    echo " <span style='color: red;'>❌ UNPROCESSED PLACEHOLDER</span>\n";
                } else {
                    echo " <span style='color: orange;'>⚠️ OTHER IMAGE</span>\n";
                }
                echo "</li>\n";
            }
            echo "</ul>\n";
        }
    }
    
    echo "<h3>Next Steps</h3>\n";
    echo "<div style='background-color: #cce5ff; padding: 15px; border: 1px solid #99ccff; border-radius: 4px;'>\n";
    echo "<h4>🔄 Please Refresh the Birthday Notification Page</h4>\n";
    echo "<ol>\n";
    echo "<li>Go back to the birthday notification page</li>\n";
    echo "<li>Refresh the page (F5 or Ctrl+R)</li>\n";
    echo "<li>Select the template again</li>\n";
    echo "<li>The preview should now show the correct member image</li>\n";
    echo "</ol>\n";
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<p>❌ <strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>\n";
}
?>
