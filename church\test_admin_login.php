<?php
session_start();
require_once 'config.php';

echo "<h2>Testing Admin Login Process</h2>";

// Test 1: Check if admin exists
echo "<h3>Test 1: Check Admin User</h3>";
try {
    $stmt = $pdo->prepare("SELECT id, username, password, email, full_name FROM admins WHERE username = ?");
    $stmt->execute(['admin']);
    $admin = $stmt->fetch();
    
    if ($admin) {
        echo "✅ Admin user found:<br>";
        echo "- ID: " . $admin['id'] . "<br>";
        echo "- Username: " . $admin['username'] . "<br>";
        echo "- Email: " . $admin['email'] . "<br>";
        echo "- Full Name: " . $admin['full_name'] . "<br>";
        echo "- Password Hash: " . substr($admin['password'], 0, 20) . "...<br>";
    } else {
        echo "❌ Admin user not found<br>";
    }
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
}

// Test 2: Test password verification
echo "<h3>Test 2: Password Verification</h3>";
if (isset($admin)) {
    $testPassword = 'admin123';
    if (password_verify($testPassword, $admin['password'])) {
        echo "✅ Password verification successful for '$testPassword'<br>";
    } else {
        echo "❌ Password verification failed for '$testPassword'<br>";
        
        // Try some other common passwords
        $commonPasswords = ['admin', 'password', '123456', 'admin123!'];
        foreach ($commonPasswords as $pwd) {
            if (password_verify($pwd, $admin['password'])) {
                echo "✅ Password verification successful for '$pwd'<br>";
                break;
            }
        }
    }
}

// Test 3: Simulate login process
echo "<h3>Test 3: Simulate Login Process</h3>";
if (isset($admin)) {
    // Clear any existing session
    session_destroy();
    session_start();
    
    // Simulate successful login
    $_SESSION['admin_id'] = $admin['id'];
    $_SESSION['admin_name'] = $admin['full_name'];
    $_SESSION['admin_username'] = $admin['username'];
    
    echo "✅ Session variables set:<br>";
    echo "- admin_id: " . $_SESSION['admin_id'] . "<br>";
    echo "- admin_name: " . $_SESSION['admin_name'] . "<br>";
    echo "- admin_username: " . $_SESSION['admin_username'] . "<br>";
}

// Test 4: Test event creation with proper admin session
echo "<h3>Test 4: Event Creation with Proper Admin Session</h3>";
if (isset($_SESSION['admin_id'])) {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO events (title, description, event_date, location,
                              max_attendees, category_id, created_by, is_active)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $result = $stmt->execute([
            'Test Event with Proper Session',
            'Test Description',
            '2025-07-20 10:00:00',
            'Test Location',
            50,
            null,
            $_SESSION['admin_id'], // Use the actual admin ID from session
            1
        ]);
        
        if ($result) {
            $event_id = $pdo->lastInsertId();
            echo "✅ Event created successfully with ID: $event_id<br>";
            
            // Clean up
            $pdo->prepare("DELETE FROM events WHERE id = ?")->execute([$event_id]);
            echo "✅ Test event cleaned up<br>";
        } else {
            echo "❌ Failed to create event<br>";
        }
        
    } catch (Exception $e) {
        echo "❌ Error creating event: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ No admin session found<br>";
}

// Test 5: Check session configuration
echo "<h3>Test 5: Session Configuration</h3>";
echo "Session ID: " . session_id() . "<br>";
echo "Session Name: " . session_name() . "<br>";
echo "Session Save Path: " . session_save_path() . "<br>";
echo "Session Cookie Params: " . print_r(session_get_cookie_params(), true) . "<br>";

echo "<h3>Test Complete</h3>";
echo "<p><a href='admin/events.php'>Go to Events Page</a></p>";
?>
