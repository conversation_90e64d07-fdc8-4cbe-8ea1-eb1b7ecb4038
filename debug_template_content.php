<?php
/**
 * Debug script to examine the birthday notification template content
 */

require_once 'config.php';

echo "<h2>Birthday Notification Template Debug</h2>\n";

try {
    // Get the specific template that's being used
    $stmt = $pdo->prepare("
        SELECT id, template_name, subject, content, template_category, is_birthday_template
        FROM email_templates 
        WHERE template_name LIKE '%Member Upcoming Birthday Notification%'
        ORDER BY id
    ");
    $stmt->execute();
    $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($templates)) {
        echo "<p>❌ No templates found matching 'Member Upcoming Birthday Notification'</p>\n";
        
        // Get all birthday templates
        $stmt = $pdo->prepare("
            SELECT id, template_name, subject, content, template_category, is_birthday_template
            FROM email_templates 
            WHERE is_birthday_template = 1
            ORDER BY template_name
        ");
        $stmt->execute();
        $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>All Birthday Templates Found:</h3>\n";
    }
    
    foreach ($templates as $template) {
        echo "<div style='border: 2px solid #007bff; margin: 20px 0; padding: 15px;'>\n";
        echo "<h3>Template: " . htmlspecialchars($template['template_name']) . "</h3>\n";
        echo "<table border='1' cellpadding='10' cellspacing='0' style='border-collapse: collapse; width: 100%;'>\n";
        echo "<tr><th>Field</th><th>Value</th></tr>\n";
        echo "<tr><td><strong>ID</strong></td><td>{$template['id']}</td></tr>\n";
        echo "<tr><td><strong>Template Name</strong></td><td>" . htmlspecialchars($template['template_name']) . "</td></tr>\n";
        echo "<tr><td><strong>Subject</strong></td><td>" . htmlspecialchars($template['subject']) . "</td></tr>\n";
        echo "<tr><td><strong>Category</strong></td><td>" . htmlspecialchars($template['template_category']) . "</td></tr>\n";
        echo "<tr><td><strong>Is Birthday Template</strong></td><td>" . ($template['is_birthday_template'] ? 'Yes' : 'No') . "</td></tr>\n";
        echo "</table>\n";
        
        echo "<h4>Template Content:</h4>\n";
        echo "<pre style='background-color: #f8f8f8; padding: 10px; border: 1px solid #ddd; white-space: pre-wrap;'>\n";
        echo htmlspecialchars($template['content']);
        echo "</pre>\n";
        
        // Check for hardcoded image URLs
        echo "<h4>Image URL Analysis:</h4>\n";
        $content = $template['content'];
        
        // Look for image tags
        preg_match_all('/<img[^>]+src=["\']([^"\']+)["\'][^>]*>/i', $content, $imgMatches);
        
        if (!empty($imgMatches[1])) {
            echo "<h5>🔍 Found Image Tags:</h5>\n";
            echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>\n";
            echo "<tr><th>Image Tag</th><th>Source URL</th><th>Type</th></tr>\n";
            
            foreach ($imgMatches[0] as $index => $fullTag) {
                $srcUrl = $imgMatches[1][$index];
                $type = 'Unknown';
                
                if (strpos($srcUrl, '{') !== false && strpos($srcUrl, '}') !== false) {
                    $type = '✅ Dynamic Placeholder';
                } elseif (strpos($srcUrl, 'localhost') !== false || strpos($srcUrl, 'uploads/') !== false) {
                    $type = '❌ Hardcoded URL';
                } elseif (strpos($srcUrl, 'http') === 0) {
                    $type = '⚠️ External URL';
                } else {
                    $type = '❓ Relative Path';
                }
                
                echo "<tr>\n";
                echo "<td><code>" . htmlspecialchars($fullTag) . "</code></td>\n";
                echo "<td><strong>" . htmlspecialchars($srcUrl) . "</strong></td>\n";
                echo "<td>$type</td>\n";
                echo "</tr>\n";
            }
            echo "</table>\n";
        } else {
            echo "<p>ℹ️ No image tags found in template content.</p>\n";
        }
        
        // Look for placeholder usage
        echo "<h5>🔍 Placeholder Analysis:</h5>\n";
        preg_match_all('/\{([^}]+)\}/', $content, $placeholderMatches);
        
        if (!empty($placeholderMatches[1])) {
            echo "<p><strong>Found Placeholders:</strong></p>\n";
            echo "<ul>\n";
            foreach (array_unique($placeholderMatches[1]) as $placeholder) {
                $isImagePlaceholder = in_array($placeholder, [
                    'member_image', 'member_image_url', 'birthday_member_image', 
                    'birthday_member_photo_url', 'image_path', 'profile_photo'
                ]);
                
                $icon = $isImagePlaceholder ? '🖼️' : '📝';
                echo "<li>$icon <code>{" . htmlspecialchars($placeholder) . "}</code>";
                if ($isImagePlaceholder) {
                    echo " <span style='color: green;'>(Image placeholder)</span>";
                }
                echo "</li>\n";
            }
            echo "</ul>\n";
        } else {
            echo "<p>⚠️ No placeholders found in template content.</p>\n";
        }
        
        echo "</div>\n";
    }
    
    // Test the template processing
    echo "<h3>Template Processing Test</h3>\n";
    
    if (!empty($templates)) {
        $testTemplate = $templates[0]; // Use the first template
        
        echo "<h4>Testing Template: " . htmlspecialchars($testTemplate['template_name']) . "</h4>\n";
        
        // Create test data similar to what would be used in birthday notifications
        $testMemberData = [
            'first_name' => 'John',
            'full_name' => 'John Doe',
            'birthday_member_first_name' => 'Ndivhuwo',
            'birthday_member_full_name' => 'Ndivhuwo Machiba',
            'birthday_member_age' => 40,
            'birthday_member_image' => 'http://localhost/campaign/church/uploads/profiles/ndivhuwo.jpg',
            'birthday_member_photo_url' => 'http://localhost/campaign/church/uploads/profiles/ndivhuwo.jpg',
            'member_image' => 'http://localhost/campaign/church/uploads/profiles/ndivhuwo.jpg',
            'member_image_url' => 'http://localhost/campaign/church/uploads/profiles/ndivhuwo.jpg',
            'image_path' => 'http://localhost/campaign/church/uploads/profiles/ndivhuwo.jpg',
            'profile_photo' => 'http://localhost/campaign/church/uploads/profiles/ndivhuwo.jpg',
            '_original_image_path' => 'uploads/profiles/ndivhuwo.jpg'
        ];
        
        // Process the template
        $processedContent = replaceTemplatePlaceholders($testTemplate['content'], $testMemberData);
        
        echo "<h5>Processed Template Content:</h5>\n";
        echo "<pre style='background-color: #f0f8ff; padding: 10px; border: 1px solid #007bff; white-space: pre-wrap;'>\n";
        echo htmlspecialchars($processedContent);
        echo "</pre>\n";
        
        echo "<h5>Visual Preview:</h5>\n";
        echo "<div style='border: 2px solid #28a745; padding: 15px; background-color: #f8f9fa;'>\n";
        echo $processedContent;
        echo "</div>\n";
        
        // Check if the processed content still has hardcoded URLs
        preg_match_all('/<img[^>]+src=["\']([^"\']+)["\'][^>]*>/i', $processedContent, $processedImgMatches);
        
        if (!empty($processedImgMatches[1])) {
            echo "<h5>🔍 Images in Processed Content:</h5>\n";
            echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>\n";
            echo "<tr><th>Image Source</th><th>Status</th></tr>\n";
            
            foreach ($processedImgMatches[1] as $srcUrl) {
                $status = 'Unknown';
                
                if (strpos($srcUrl, 'uploads/profiles/ndivhuwo.jpg') !== false) {
                    $status = '✅ Correct member image';
                } elseif (strpos($srcUrl, 'uploads/6850') !== false || strpos($srcUrl, 'uploads/67c5912233dc9.jpg') !== false) {
                    $status = '❌ Wrong hardcoded image';
                } elseif (strpos($srcUrl, '{') !== false) {
                    $status = '⚠️ Unprocessed placeholder';
                } else {
                    $status = '❓ Other image';
                }
                
                echo "<tr>\n";
                echo "<td><strong>" . htmlspecialchars($srcUrl) . "</strong></td>\n";
                echo "<td>$status</td>\n";
                echo "</tr>\n";
            }
            echo "</table>\n";
        }
    }
    
} catch (Exception $e) {
    echo "<p>❌ <strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>\n";
}
?>
