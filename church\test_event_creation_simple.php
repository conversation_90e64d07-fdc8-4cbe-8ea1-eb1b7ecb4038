<?php
session_start();
require_once 'config.php';

// Simple test to create an event after manual login
echo "<h2>Simple Event Creation Test</h2>";

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    echo "<p>❌ Admin not logged in. Please <a href='admin/login.php'>login first</a>, then come back to this page.</p>";
    echo "<p>Current session data: " . print_r($_SESSION, true) . "</p>";
    exit;
}

echo "<p>✅ Admin logged in: ID=" . $_SESSION['admin_id'] . ", Name=" . $_SESSION['admin_name'] . "</p>";

// Test event creation
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<h3>Creating Event...</h3>";
    
    try {
        // Start transaction
        $pdo->beginTransaction();
        
        // Create the event
        $stmt = $pdo->prepare("
            INSERT INTO events (title, description, event_date, location,
                              max_attendees, category_id, created_by, is_active)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $result = $stmt->execute([
            $_POST['title'],
            $_POST['description'],
            $_POST['start_datetime'],
            $_POST['location'],
            !empty($_POST['capacity']) ? (int)$_POST['capacity'] : null,
            !empty($_POST['category_id']) ? (int)$_POST['category_id'] : null,
            $_SESSION['admin_id'],
            ($_POST['status'] === 'published') ? 1 : 0
        ]);
        
        $event_id = $pdo->lastInsertId();
        
        // Commit transaction
        $pdo->commit();
        
        echo "<p>✅ Event created successfully with ID: $event_id</p>";
        
        // Clean up test event
        $pdo->prepare("DELETE FROM events WHERE id = ?")->execute([$event_id]);
        echo "<p>✅ Test event cleaned up</p>";
        
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollback();
        }
        echo "<p>❌ Error: " . $e->getMessage() . "</p>";
    }
} else {
    // Show form
    ?>
    <form method="POST">
        <h3>Create Test Event</h3>
        <p>Title: <input type="text" name="title" value="Test Event Simple" required></p>
        <p>Description: <textarea name="description">Test Description</textarea></p>
        <p>Start Date/Time: <input type="datetime-local" name="start_datetime" value="2025-07-20T10:00" required></p>
        <p>Location: <input type="text" name="location" value="Test Location"></p>
        <p>Capacity: <input type="number" name="capacity" value="50"></p>
        <p>Status: 
            <select name="status">
                <option value="published">Published</option>
                <option value="draft">Draft</option>
            </select>
        </p>
        <p><button type="submit">Create Event</button></p>
    </form>
    <?php
}
?>
