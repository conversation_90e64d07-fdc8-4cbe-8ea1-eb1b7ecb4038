<?php
// Create a simple test image
$width = 200;
$height = 100;

// Create a blank image
$image = imagecreate($width, $height);

// Allocate colors
$white = imagecolorallocate($image, 255, 255, 255);
$black = imagecolorallocate($image, 0, 0, 0);
$red = imagecolorallocate($image, 255, 0, 0);

// Fill background
imagefill($image, 0, 0, $white);

// Add some text
imagestring($image, 5, 50, 30, 'TEST IMAGE', $black);
imagestring($image, 3, 60, 60, 'For Upload Test', $red);

// Save the image
$filename = 'test_image.jpg';
imagejpeg($image, $filename, 90);

// Clean up
imagedestroy($image);

echo "Test image created: $filename (" . filesize($filename) . " bytes)";
?>
